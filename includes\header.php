<?php
require_once dirname(__DIR__) . '/includes/init.php';
check_login();
check_page_permissions();
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المشتركين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="<?php echo get_path('/assets/css/style.css'); ?>" rel="stylesheet">
    <link href="<?php echo get_path('/assets/css/tables.css'); ?>" rel="stylesheet">
    <link href="<?php echo get_path('/assets/css/modern.css'); ?>" rel="stylesheet">

    <style>
    /* إصلاح مشكلة z-index للقائمة المنسدلة */
    .navbar {
        z-index: 1050 !important;
        position: relative !important;
    }

    .dropdown-menu {
        z-index: 1060 !important;
        position: absolute !important;
        background: rgba(255, 255, 255, 0.98) !important;
        backdrop-filter: blur(10px) !important;
        border: 1px solid rgba(0,0,0,0.1) !important;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2) !important;
        border-radius: 10px !important;
    }

    .dropdown-item {
        transition: all 0.3s ease !important;
        border-radius: 8px !important;
        margin: 2px 5px !important;
    }

    .dropdown-item:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        color: white !important;
        transform: translateX(5px) !important;
    }

    /* التأكد من أن المحتوى لا يتداخل مع الشريط العلوي */
    .container-fluid.py-4 {
        z-index: 1 !important;
        position: relative !important;
    }

    .card.header-card {
        z-index: 1 !important;
        position: relative !important;
    }
    </style>
</head>
<body>
    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show m-3" role="alert">
            <?php 
            echo $_SESSION['error'];
            unset($_SESSION['error']);
            ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show m-3" role="alert">
            <?php 
            echo $_SESSION['success'];
            unset($_SESSION['success']);
            ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <nav class="navbar navbar-expand-lg navbar-dark shadow-sm" style="background-color: #0B8582 !important;">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-people-fill me-2"></i>
                نظام إدارة المشتركين
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo is_active_page('dashboard.php'); ?>" 
                           href="<?php echo get_path('/pages/dashboard.php'); ?>">
                            <i class="bi bi-house-door-fill"></i> الرئيسية
                        </a>
                    </li>

                    <?php if (currentUserHasPermission('agents.view_all')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo is_active_page('agents.php'); ?>"
                           href="<?php echo get_path('/pages/agents.php'); ?>">
                            <i class="bi bi-people-fill"></i> المشتركين
                        </a>
                    </li>
                    <?php endif; ?>



                    <?php if (currentUserHasPermission('reports.export')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo is_active_page('export.php'); ?>"
                           href="<?php echo get_path('/pages/export.php'); ?>">
                            <i class="bi bi-file-earmark-excel-fill"></i> تصدير البيانات
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php if (currentUserHasPermission('system.admin_access')): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" 
                           data-bs-toggle="dropdown">
                            <i class="bi bi-gear-fill"></i> إدارة النظام
                        </a>
                        <ul class="dropdown-menu">



                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item <?php echo is_active_page('cabinet_locations.php'); ?>"
                                   href="<?php echo get_path('/pages/cabinet_locations.php'); ?>">
                                    <i class="bi bi-building me-2"></i> مكان كابينة
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item <?php echo is_active_page('service_types.php'); ?>"
                                   href="<?php echo get_path('/pages/service_types.php'); ?>">
                                    <i class="bi bi-gear-wide-connected me-2"></i> نوع الخدمة
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item <?php echo is_active_page('tower_locations.php'); ?>"
                                   href="<?php echo get_path('/pages/tower_locations.php'); ?>">
                                    <i class="bi bi-broadcast-pin me-2"></i> مكان البرج
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item <?php echo is_active_page('belongs_to_options.php'); ?>"
                                   href="<?php echo get_path('/pages/belongs_to_options.php'); ?>">
                                    <i class="bi bi-diagram-3 me-2"></i> تابع الى
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item <?php echo is_active_page('ports.php'); ?>"
                                   href="<?php echo get_path('/pages/ports.php'); ?>">
                                    <i class="bi bi-hdd-network me-2"></i> Port
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item <?php echo is_active_page('discounts.php'); ?>"
                                   href="<?php echo get_path('/pages/discounts.php'); ?>">
                                    <i class="bi bi-percent me-2"></i> الخصم
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item <?php echo is_active_page('branch_names.php'); ?>"
                                   href="<?php echo get_path('/pages/branch_names.php'); ?>">
                                    <i class="bi bi-building me-2"></i> اسم الفرع
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item <?php echo is_active_page('activity_log.php'); ?>"
                                   href="<?php echo get_path('/pages/activity_log.php'); ?>">
                                    <i class="bi bi-journal-text me-2"></i> سجل النشاطات
                                </a>
                            </li>
                        </ul>
                    </li>
                    <?php endif; ?>


                </ul>

                <ul class="navbar-nav">



                    <!-- قائمة المستخدم -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                           data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i>
                            <?php echo htmlspecialchars($_SESSION['user_name']); ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="<?php echo get_path('/pages/profile.php'); ?>">
                                    <i class="bi bi-person-badge me-2"></i> الملف الشخصي
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-danger" href="<?php echo get_path('/includes/logout.php'); ?>">
                                    <i class="bi bi-box-arrow-right me-2"></i> تسجيل الخروج
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <style>
    .notifications-dropdown {
        border: none;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .notification-item {
        padding: 12px 16px;
        border-bottom: 1px solid #f0f0f0;
        transition: background-color 0.2s ease;
    }

    .notification-item:hover {
        background-color: #f8f9fa;
    }

    .notification-item.unread {
        background-color: #f8f9ff;
        border-left: 3px solid #007bff;
    }

    .notification-icon {
        font-size: 1.1em;
    }

    .notification-title {
        line-height: 1.3;
    }

    .notification-message {
        line-height: 1.2;
        margin-top: 2px;
    }

    .notification-time {
        margin-top: 4px;
        font-size: 0.75rem;
    }

    .notification-badge {
        margin-top: 4px;
    }

    .dropdown-header {
        font-weight: 600;
        color: #495057;
        padding: 12px 16px;
    }

    .dropdown-footer {
        padding: 8px 12px;
    }
    </style>

    <?php
    // دالة لحساب الوقت المنقضي
    function timeAgo($datetime) {
        $time = time() - strtotime($datetime);

        if ($time < 60) return 'الآن';
        if ($time < 3600) return floor($time/60) . ' د';
        if ($time < 86400) return floor($time/3600) . ' س';
        if ($time < 2592000) return floor($time/86400) . ' ي';
        if ($time < 31536000) return floor($time/2592000) . ' ش';
        return floor($time/31536000) . ' سنة';
    }
    ?>

    <div class="container mt-4">