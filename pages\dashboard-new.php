<?php
require_once '../includes/init.php';

// جلب الإحصائيات
$backup_count_query = executeQuerySafely($conn, "SELECT COUNT(*) as count FROM backups");
$backup_count = $backup_count_query ? $backup_count_query->fetch_assoc()['count'] : 0;

$tickets_query = executeQuerySafely($conn, "
    SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status IN ('new', 'in_progress') THEN 1 ELSE 0 END) as open
    FROM tickets
");
$tickets_stats = $tickets_query ? $tickets_query->fetch_assoc() : ['total' => 0, 'open' => 0];

$service_types_query = executeQuerySafely($conn, "SELECT COUNT(*) as total FROM service_types WHERE status = 'active'");
$service_types_count = $service_types_query ? $service_types_query->fetch_assoc()['total'] : 0;

$tower_locations_query = executeQuerySafely($conn, "SELECT COUNT(*) as total FROM tower_locations WHERE status = 'active'");
$tower_locations_count = $tower_locations_query ? $tower_locations_query->fetch_assoc()['total'] : 0;

$branch_names_query = executeQuerySafely($conn, "SELECT COUNT(*) as total FROM branch_names WHERE status = 'active'");
$branch_names_count = $branch_names_query ? $branch_names_query->fetch_assoc()['total'] : 0;

// جلب آخر المشتركين
$recent_agents_query = executeQuerySafely($conn, "
    SELECT name, phone, address, status, created_at 
    FROM agents 
    ORDER BY created_at DESC 
    LIMIT 8
");
$recent_agents = $recent_agents_query ? $recent_agents_query->fetch_all(MYSQLI_ASSOC) : [];

include '../includes/header-new.php';
?>

<!-- رأس الصفحة -->
<div class="page-header" data-aos="fade-down">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">لوحة التحكم الرئيسية</h1>
                <p class="page-subtitle">مرحباً بك في النظام الجديد - إدارة شاملة ومتطورة</p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="d-flex align-items-center justify-content-md-end gap-3">
                    <div class="text-muted">
                        <i class="fas fa-clock me-2"></i>
                        <span id="current-time"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الإجراءات السريعة -->
<div class="quick-actions" data-aos="fade-up" data-aos-delay="100">
    <a href="<?php echo get_path('/pages/agents.php'); ?>" class="quick-action-btn">
        <div class="quick-action-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <i class="fas fa-users"></i>
        </div>
        <div>
            <h6 class="mb-1">إدارة المشتركين</h6>
            <small class="text-muted">عرض وإدارة جميع المشتركين</small>
        </div>
    </a>
    
    <a href="<?php echo get_path('/pages/backup.php'); ?>" class="quick-action-btn">
        <div class="quick-action-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
            <i class="fas fa-database"></i>
        </div>
        <div>
            <h6 class="mb-1">النسخ الاحتياطية</h6>
            <small class="text-muted">إدارة نسخ البيانات الاحتياطية</small>
        </div>
    </a>
    
    <a href="<?php echo get_path('/pages/tickets.php'); ?>" class="quick-action-btn">
        <div class="quick-action-icon" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
            <i class="fas fa-ticket-alt"></i>
        </div>
        <div>
            <h6 class="mb-1">نظام التذاكر</h6>
            <small class="text-muted">متابعة طلبات الدعم</small>
        </div>
    </a>
</div>

<!-- بطاقات الإحصائيات -->
<div class="row g-4 mb-4">
    <!-- النسخ الاحتياطية -->
    <div class="col-xl-3 col-md-6" data-aos="zoom-in" data-aos-delay="200">
        <a href="<?php echo get_path('/pages/backup.php'); ?>" class="stats-card primary">
            <div class="card-body">
                <div class="stats-icon">
                    <i class="fas fa-database"></i>
                </div>
                <div class="stats-number"><?php echo number_format($backup_count); ?></div>
                <div class="stats-label">النسخ الاحتياطية</div>
                <div class="stats-change text-success">
                    <i class="fas fa-arrow-up me-1"></i>
                    متوفرة ومحدثة
                </div>
            </div>
        </a>
    </div>

    <!-- التذاكر -->
    <div class="col-xl-3 col-md-6" data-aos="zoom-in" data-aos-delay="300">
        <a href="<?php echo get_path('/pages/tickets.php'); ?>" class="stats-card warning">
            <div class="card-body">
                <div class="stats-icon">
                    <i class="fas fa-ticket-alt"></i>
                </div>
                <div class="stats-number"><?php echo number_format($tickets_stats['total']); ?></div>
                <div class="stats-label">إجمالي التذاكر</div>
                <div class="stats-change text-warning">
                    <i class="fas fa-exclamation-circle me-1"></i>
                    <?php echo number_format($tickets_stats['open']); ?> مفتوحة
                </div>
            </div>
        </a>
    </div>

    <!-- أنواع الخدمة -->
    <div class="col-xl-3 col-md-6" data-aos="zoom-in" data-aos-delay="400">
        <a href="<?php echo get_path('/pages/service_types.php'); ?>" class="stats-card success">
            <div class="card-body">
                <div class="stats-icon">
                    <i class="fas fa-concierge-bell"></i>
                </div>
                <div class="stats-number"><?php echo number_format($service_types_count); ?></div>
                <div class="stats-label">أنواع الخدمة</div>
                <div class="stats-change text-success">
                    <i class="fas fa-check-circle me-1"></i>
                    نشطة
                </div>
            </div>
        </a>
    </div>

    <!-- مواقع الأبراج -->
    <div class="col-xl-3 col-md-6" data-aos="zoom-in" data-aos-delay="500">
        <a href="<?php echo get_path('/pages/tower_locations.php'); ?>" class="stats-card secondary">
            <div class="card-body">
                <div class="stats-icon">
                    <i class="fas fa-tower-broadcast"></i>
                </div>
                <div class="stats-number"><?php echo number_format($tower_locations_count); ?></div>
                <div class="stats-label">مواقع الأبراج</div>
                <div class="stats-change text-info">
                    <i class="fas fa-signal me-1"></i>
                    متاحة
                </div>
            </div>
        </a>
    </div>
</div>

<!-- الصف الثاني من البطاقات -->
<div class="row g-4 mb-4">
    <!-- أسماء الفروع -->
    <div class="col-xl-3 col-md-6" data-aos="zoom-in" data-aos-delay="600">
        <a href="<?php echo get_path('/pages/branch_names.php'); ?>" class="stats-card danger">
            <div class="card-body">
                <div class="stats-icon">
                    <i class="fas fa-building"></i>
                </div>
                <div class="stats-number"><?php echo number_format($branch_names_count); ?></div>
                <div class="stats-label">أسماء الفروع</div>
                <div class="stats-change text-primary">
                    <i class="fas fa-map-marker-alt me-1"></i>
                    مسجلة
                </div>
            </div>
        </a>
    </div>

    <!-- إحصائية إضافية 1 -->
    <div class="col-xl-3 col-md-6" data-aos="zoom-in" data-aos-delay="700">
        <div class="stats-card primary">
            <div class="card-body">
                <div class="stats-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stats-number">98%</div>
                <div class="stats-label">معدل الأداء</div>
                <div class="stats-change text-success">
                    <i class="fas fa-arrow-up me-1"></i>
                    ممتاز
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائية إضافية 2 -->
    <div class="col-xl-3 col-md-6" data-aos="zoom-in" data-aos-delay="800">
        <div class="stats-card success">
            <div class="card-body">
                <div class="stats-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="stats-number">100%</div>
                <div class="stats-label">الأمان</div>
                <div class="stats-change text-success">
                    <i class="fas fa-lock me-1"></i>
                    محمي
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائية إضافية 3 -->
    <div class="col-xl-3 col-md-6" data-aos="zoom-in" data-aos-delay="900">
        <div class="stats-card warning">
            <div class="card-body">
                <div class="stats-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stats-number">24/7</div>
                <div class="stats-label">الخدمة</div>
                <div class="stats-change text-info">
                    <i class="fas fa-infinity me-1"></i>
                    مستمرة
                </div>
            </div>
        </div>
    </div>
</div>

<!-- آخر المشتركين -->
<div class="row">
    <div class="col-12" data-aos="fade-up" data-aos-delay="1000">
        <div class="modern-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="card-title gradient-text">
                        <i class="fas fa-users me-2"></i>
                        آخر المشتركين المضافين
                    </h4>
                    <a href="<?php echo get_path('/pages/agents.php'); ?>" class="modern-btn primary">
                        عرض الكل
                        <i class="fas fa-arrow-left"></i>
                    </a>
                </div>

                <?php if (!empty($recent_agents)): ?>
                <div class="modern-table">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>المشترك</th>
                                <th>رقم الهاتف</th>
                                <th>العنوان</th>
                                <th>الحالة</th>
                                <th>تاريخ الإضافة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_agents as $agent): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="bg-primary bg-opacity-10 rounded-circle p-2 me-3">
                                            <i class="fas fa-user text-primary"></i>
                                        </div>
                                        <strong><?php echo htmlspecialchars($agent['name']); ?></strong>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark">
                                        <i class="fas fa-phone me-1"></i>
                                        <?php echo htmlspecialchars($agent['phone']); ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars($agent['address']); ?></td>
                                <td>
                                    <?php if ($agent['status'] === 'active'): ?>
                                        <span class="badge bg-success">
                                            <i class="fas fa-check-circle me-1"></i>نشط
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-pause-circle me-1"></i>غير نشط
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        <?php echo date('Y-m-d H:i', strtotime($agent['created_at'])); ?>
                                    </small>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-users text-muted" style="font-size: 4rem; opacity: 0.3;"></i>
                    <h5 class="text-muted mt-3">لا توجد بيانات مشتركين</h5>
                    <p class="text-muted">ابدأ بإضافة المشتركين الجدد</p>
                    <a href="<?php echo get_path('/pages/agents.php'); ?>" class="modern-btn primary">
                        <i class="fas fa-plus me-2"></i>إضافة مشترك جديد
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer-new.php'; ?>
