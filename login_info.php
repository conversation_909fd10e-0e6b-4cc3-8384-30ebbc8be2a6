<?php
$page_title = "معلومات تسجيل الدخول - قاعدة البيانات المحسنة";
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .login-info-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .info-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }
        .card-header-custom {
            background: linear-gradient(135deg, #01233D 0%, #0B8582 100%);
            color: white;
            padding: 25px;
            text-align: center;
        }
        .login-credentials {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            text-align: center;
            font-size: 1.2rem;
        }
        .credential-box {
            background: rgba(255,255,255,0.2);
            border-radius: 15px;
            padding: 20px;
            margin: 10px;
            backdrop-filter: blur(10px);
        }
        .warning-section {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            color: white;
            padding: 25px;
        }
        .data-section {
            padding: 25px;
        }
        .data-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #0B8582;
        }
        .copy-btn {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            border-radius: 8px;
            padding: 5px 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .copy-btn:hover {
            background: rgba(255,255,255,0.3);
            color: white;
        }
        .step-card {
            background: linear-gradient(135deg, #38C5E6 0%, #0B8582 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            text-align: center;
        }
        .step-number {
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-weight: bold;
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <div class="login-info-container">
        <!-- Header -->
        <div class="info-card">
            <div class="card-header-custom">
                <h1><i class="fas fa-key me-3"></i><?php echo $page_title; ?></h1>
                <p class="mb-0">جميع المعلومات اللازمة لتسجيل الدخول والبدء في استخدام النظام</p>
            </div>
        </div>

        <!-- بيانات الدخول -->
        <div class="info-card">
            <div class="login-credentials">
                <h2><i class="fas fa-user-shield me-2"></i>بيانات المستخدم الإداري</h2>
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="credential-box">
                            <h5><i class="fas fa-user me-2"></i>اسم المستخدم</h5>
                            <div class="d-flex align-items-center justify-content-center">
                                <code style="font-size: 1.5rem; background: none; color: white;">admin</code>
                                <button class="copy-btn ms-2" onclick="copyToClipboard('admin')">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="credential-box">
                            <h5><i class="fas fa-lock me-2"></i>كلمة المرور</h5>
                            <div class="d-flex align-items-center justify-content-center">
                                <code style="font-size: 1.5rem; background: none; color: white;">password</code>
                                <button class="copy-btn ms-2" onclick="copyToClipboard('password')">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <p><strong>الاسم الكامل:</strong> المدير العام</p>
                    <p><strong>البريد الإلكتروني:</strong> <EMAIL></p>
                    <p><strong>الدور:</strong> مدير (جميع الصلاحيات)</p>
                </div>
            </div>
        </div>

        <!-- تحذير أمني -->
        <div class="info-card">
            <div class="warning-section">
                <h3><i class="fas fa-exclamation-triangle me-2"></i>تحذير أمني مهم!</h3>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h5>⚠️ يجب تغيير كلمة المرور فوراً:</h5>
                        <ul>
                            <li>هذه كلمة مرور افتراضية</li>
                            <li>معروفة للجميع</li>
                            <li>غير آمنة للاستخدام الفعلي</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>🛡️ إعدادات الأمان الموصى بها:</h5>
                        <ul>
                            <li>تغيير اسم المستخدم</li>
                            <li>تحديث البريد الإلكتروني</li>
                            <li>إنشاء مستخدمين إضافيين</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- خطوات البدء السريع -->
        <div class="info-card">
            <div class="data-section">
                <h3 class="text-center mb-4"><i class="fas fa-rocket me-2"></i>خطوات البدء السريع</h3>
                <div class="row">
                    <div class="col-md-3">
                        <div class="step-card">
                            <div class="step-number">1</div>
                            <h5>تسجيل الدخول</h5>
                            <p class="mb-0">استخدم البيانات أعلاه</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="step-card">
                            <div class="step-number">2</div>
                            <h5>تغيير كلمة المرور</h5>
                            <p class="mb-0">من الإعدادات الشخصية</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="step-card">
                            <div class="step-number">3</div>
                            <h5>استكشاف النظام</h5>
                            <p class="mb-0">تصفح المشتركين والتقارير</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="step-card">
                            <div class="step-number">4</div>
                            <h5>إضافة مستخدمين</h5>
                            <p class="mb-0">إنشاء حسابات للموظفين</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- البيانات التجريبية -->
        <div class="info-card">
            <div class="data-section">
                <h3><i class="fas fa-database me-2"></i>البيانات التجريبية المتوفرة</h3>
                <div class="row">
                    <div class="col-md-6">
                        <div class="data-item">
                            <h5><i class="fas fa-wifi text-primary me-2"></i>أنواع الخدمات (4)</h5>
                            <ul class="mb-0">
                                <li>فايبر منزلي - 25,000 دينار</li>
                                <li>فايبر تجاري - 50,000 دينار</li>
                                <li>وايرلس - 20,000 دينار</li>
                                <li>ADSL - 15,000 دينار</li>
                            </ul>
                        </div>
                        <div class="data-item">
                            <h5><i class="fas fa-building text-success me-2"></i>الفروع (5)</h5>
                            <p class="mb-0">المكتب الرئيسي، النجف، كربلاء، بابل، بغداد</p>
                        </div>
                        <div class="data-item">
                            <h5><i class="fas fa-map-marker-alt text-danger me-2"></i>الكابينات والأبراج</h5>
                            <p class="mb-0">5 مواقع كابينات و 5 مواقع أبراج</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="data-item">
                            <h5><i class="fas fa-percentage text-warning me-2"></i>الخصومات (5)</h5>
                            <p class="mb-0">من 0% إلى 20% للعملاء المختلفين</p>
                        </div>
                        <div class="data-item">
                            <h5><i class="fas fa-handshake text-info me-2"></i>خيارات التبعية (5)</h5>
                            <p class="mb-0">مباشر، وكيل، شركة تابعة، شراكة</p>
                        </div>
                        <div class="data-item">
                            <h5><i class="fas fa-user text-secondary me-2"></i>مشترك تجريبي</h5>
                            <p class="mb-0"><strong>فندق القمر</strong> - 07714403444</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- روابط مفيدة -->
        <div class="info-card">
            <div class="data-section text-center">
                <h3><i class="fas fa-link me-2"></i>روابط مفيدة</h3>
                <div class="row mt-4">
                    <div class="col-md-4">
                        <a href="pages/login.php" class="btn btn-primary btn-lg w-100 mb-3">
                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="database_report.php" class="btn btn-info btn-lg w-100 mb-3">
                            <i class="fas fa-chart-bar me-2"></i>تقرير قاعدة البيانات
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="database_comparison.php" class="btn btn-success btn-lg w-100 mb-3">
                            <i class="fas fa-balance-scale me-2"></i>مقارنة التحسينات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // إظهار رسالة نجاح
                const toast = document.createElement('div');
                toast.className = 'toast-message';
                toast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #28a745;
                    color: white;
                    padding: 15px 25px;
                    border-radius: 10px;
                    z-index: 9999;
                    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
                `;
                toast.innerHTML = '<i class="fas fa-check me-2"></i>تم النسخ بنجاح!';
                document.body.appendChild(toast);
                
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 2000);
            });
        }
    </script>
</body>
</html>
