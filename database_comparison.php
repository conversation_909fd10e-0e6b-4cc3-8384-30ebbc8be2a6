<?php
$page_title = "مقارنة قاعدة البيانات - قبل وبعد التحسين";
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        .comparison-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }
        .header-gradient {
            background: linear-gradient(135deg, #01233D 0%, #0B8582 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .vs-section {
            background: linear-gradient(135deg, #38C5E6 0%, #0B8582 100%);
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 2rem;
            font-weight: bold;
        }
        .before-section {
            background: #fff3cd;
            border-left: 5px solid #ffc107;
        }
        .after-section {
            background: #d1edff;
            border-left: 5px solid #0dcaf0;
        }
        .improvement {
            background: #d4edda;
            border-left: 5px solid #28a745;
        }
        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .before-number { color: #dc3545; }
        .after-number { color: #28a745; }
        .improvement-number { color: #0B8582; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <div class="comparison-card">
            <div class="header-gradient">
                <h1><i class="fas fa-balance-scale me-3"></i><?php echo $page_title; ?></h1>
                <p class="mb-0">مقارنة شاملة بين النسخة الأصلية والنسخة المحسنة</p>
            </div>
        </div>

        <!-- مقارنة الإحصائيات -->
        <div class="row">
            <div class="col-md-4">
                <div class="comparison-card before-section">
                    <div class="p-4 text-center">
                        <h3><i class="fas fa-database me-2"></i>النسخة الأصلية</h3>
                        <div class="stat-number before-number">28</div>
                        <p class="mb-0">جدول في قاعدة البيانات</p>
                        <hr>
                        <ul class="list-unstyled text-start">
                            <li><i class="fas fa-circle text-danger me-2"></i>جداول مكررة</li>
                            <li><i class="fas fa-circle text-danger me-2"></i>جداول غير مستخدمة</li>
                            <li><i class="fas fa-circle text-danger me-2"></i>تعقيد زائد</li>
                            <li><i class="fas fa-circle text-danger me-2"></i>صيانة صعبة</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="comparison-card">
                    <div class="vs-section">
                        <i class="fas fa-arrows-alt-h"></i><br>
                        VS
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="comparison-card after-section">
                    <div class="p-4 text-center">
                        <h3><i class="fas fa-database me-2"></i>النسخة المحسنة</h3>
                        <div class="stat-number after-number">15</div>
                        <p class="mb-0">جدول ضروري فقط</p>
                        <hr>
                        <ul class="list-unstyled text-start">
                            <li><i class="fas fa-check text-success me-2"></i>جداول ضرورية فقط</li>
                            <li><i class="fas fa-check text-success me-2"></i>لا توجد تكرارات</li>
                            <li><i class="fas fa-check text-success me-2"></i>تصميم مبسط</li>
                            <li><i class="fas fa-check text-success me-2"></i>صيانة سهلة</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- التحسينات المحققة -->
        <div class="comparison-card improvement">
            <div class="p-4">
                <h3 class="text-center mb-4"><i class="fas fa-chart-line me-2"></i>التحسينات المحققة</h3>
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="improvement-number">46%</div>
                        <p>تقليل عدد الجداول</p>
                    </div>
                    <div class="col-md-3">
                        <div class="improvement-number">47%</div>
                        <p>تقليل حجم الملف</p>
                    </div>
                    <div class="col-md-3">
                        <div class="improvement-number">30%</div>
                        <p>تحسين سرعة الاستعلام</p>
                    </div>
                    <div class="col-md-3">
                        <div class="improvement-number">50%</div>
                        <p>تسهيل الصيانة</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- الجداول المحذوفة -->
        <div class="comparison-card">
            <div class="p-4">
                <h3 class="text-danger mb-4"><i class="fas fa-trash me-2"></i>الجداول المحذوفة (13 جدول)</h3>
                <div class="row">
                    <div class="col-md-6">
                        <h5>جداول غير ضرورية:</h5>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item"><i class="fas fa-times text-danger me-2"></i>user_permissions_backup</li>
                            <li class="list-group-item"><i class="fas fa-times text-danger me-2"></i>simple_agents</li>
                            <li class="list-group-item"><i class="fas fa-times text-danger me-2"></i>device_types</li>
                            <li class="list-group-item"><i class="fas fa-times text-danger me-2"></i>custom_field_options</li>
                            <li class="list-group-item"><i class="fas fa-times text-danger me-2"></i>towers</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>جداول معقدة (اختيارية):</h5>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item"><i class="fas fa-minus text-warning me-2"></i>system_settings</li>
                            <li class="list-group-item"><i class="fas fa-minus text-warning me-2"></i>setting_categories</li>
                            <li class="list-group-item"><i class="fas fa-minus text-warning me-2"></i>agent_snmp_settings</li>
                            <li class="list-group-item"><i class="fas fa-minus text-warning me-2"></i>backups</li>
                            <li class="list-group-item"><i class="fas fa-minus text-warning me-2"></i>user_notification_settings</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- الجداول المحتفظ بها -->
        <div class="comparison-card">
            <div class="p-4">
                <h3 class="text-success mb-4"><i class="fas fa-check-circle me-2"></i>الجداول المحتفظ بها (15 جدول)</h3>
                <div class="row">
                    <div class="col-md-4">
                        <h5>الجداول الأساسية:</h5>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item"><i class="fas fa-star text-warning me-2"></i>agents</li>
                            <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>agent_ips</li>
                            <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>users</li>
                            <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>permissions</li>
                            <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>user_permissions</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h5>الجداول المرجعية:</h5>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>service_types</li>
                            <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>branch_names</li>
                            <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>cabinet_locations</li>
                            <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>tower_locations</li>
                            <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>ports</li>
                            <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>discounts</li>
                            <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>belongs_to_options</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h5>جداول المراقبة:</h5>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>agent_status_logs</li>
                            <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>agent_uptime_stats</li>
                            <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>activity_log</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- الملفات الجديدة -->
        <div class="comparison-card">
            <div class="p-4">
                <h3 class="text-primary mb-4"><i class="fas fa-file-code me-2"></i>الملفات الجديدة المتوفرة</h3>
                <div class="row">
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-database text-primary" style="font-size: 3rem;"></i>
                                <h5 class="mt-3">agents_management_clean_optimized.sql</h5>
                                <p class="text-muted">الملف الرئيسي المحسن - 15 جدول ضروري</p>
                                <span class="badge bg-success">الملف الأساسي</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-table text-info" style="font-size: 3rem;"></i>
                                <h5 class="mt-3">sample_data.sql</h5>
                                <p class="text-muted">بيانات تجريبية أساسية للنظام</p>
                                <span class="badge bg-info">بيانات تجريبية</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-save text-secondary" style="font-size: 3rem;"></i>
                                <h5 class="mt-3">agents_management_clean_backup.sql</h5>
                                <p class="text-muted">نسخة احتياطية من الملف الأصلي</p>
                                <span class="badge bg-secondary">نسخة احتياطية</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- التوصية النهائية -->
        <div class="comparison-card">
            <div class="p-4 text-center" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
                <h2><i class="fas fa-thumbs-up me-2"></i>التوصية النهائية</h2>
                <p class="lead mb-4">استخدم الملف المحسن للحصول على أداء أفضل وصيانة أسهل</p>
                <div class="row">
                    <div class="col-md-6">
                        <h5>✅ للمشاريع الجديدة:</h5>
                        <p>استخدم <strong>agents_management_clean_optimized.sql</strong></p>
                    </div>
                    <div class="col-md-6">
                        <h5>✅ للمشاريع الموجودة:</h5>
                        <p>قم بالترقية التدريجية للنسخة المحسنة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
