-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Jul 22, 2025 at 12:32 PM
-- Server version: 8.0.40
-- PHP Version: 8.3.18

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `agents_management_clean`
--

-- --------------------------------------------------------

--
-- Table structure for table `activity_log`
--

CREATE TABLE `activity_log` (
  `id` int NOT NULL,
  `user_id` int DEFAULT NULL,
  `action` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `activity_log`
--

INSERT INTO `activity_log` (`id`, `user_id`, `action`, `description`, `created_at`) VALUES
(2, 1, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-06-28 13:57:08'),
(3, 1, 'تعديل وكيل', 'تم تعديل بيانات الوكيل: مصطفى حسن رضا (ID: 351)', '2025-06-28 13:58:08'),
(4, 1, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-06-28 14:01:16'),
(5, NULL, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-06-28 14:01:26'),
(6, 1, 'إضافة موظف', 'تم إضافة موظف جديد: علي حمزه محمد (ali)', '2025-06-28 14:22:37'),
(7, 1, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-06-28 14:23:31'),
(8, NULL, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-06-28 14:26:08'),
(9, 1, 'ticket_deleted', 'تم حذف التذكرة: 3', '2025-06-28 14:27:43'),
(10, 1, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-06-28 14:30:08'),
(11, NULL, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-06-28 14:30:30'),
(12, 1, 'إضافة موظف', 'تم إضافة موظف جديد: علي حمزه محمد (ali)', '2025-06-28 14:32:57'),
(13, 1, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-06-28 14:33:00'),
(14, NULL, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-06-28 14:42:17'),
(15, 1, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-06-28 14:43:24'),
(16, NULL, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-06-28 14:46:34'),
(17, NULL, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-06-28 14:46:43'),
(18, 1, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-06-28 14:48:19'),
(19, 8, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-06-28 14:49:45'),
(20, 1, 'إضافة وكيل', 'تمت إضافة وكيل جديد: قاسم علي', '2025-06-28 14:52:33'),
(21, 1, 'ticket_deleted', 'تم حذف التذكرة: 2', '2025-06-28 14:54:18'),
(22, 1, 'ticket_deleted', 'تم حذف التذكرة: 1', '2025-06-28 14:54:29'),
(23, 1, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-06-28 15:01:32'),
(24, 1, 'إضافة موظف', 'تم إضافة موظف جديد: علي حمزه محمد (ali)', '2025-06-28 15:04:21'),
(25, 1, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-06-28 15:04:25'),
(26, 14, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-06-28 15:05:15'),
(27, 1, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-06-28 15:06:27'),
(28, 14, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-06-28 15:07:06'),
(29, 1, 'إضافة وكيل', 'تمت إضافة وكيل جديد: طلال صادق جعفر', '2025-06-29 12:33:44'),
(30, 1, 'ticket_created', 'تم إنشاء تذكرة جديدة: 1', '2025-06-30 17:47:04'),
(31, 1, 'ticket_created', 'تم إنشاء تذكرة جديدة: 2', '2025-06-30 17:48:39'),
(32, 1, 'ticket_created', 'تم إنشاء تذكرة جديدة: 3', '2025-06-30 17:49:48'),
(33, 1, 'ticket_created', 'تم إنشاء تذكرة جديدة: 4', '2025-06-30 17:50:47'),
(34, 1, 'ticket_created', 'تم إنشاء تذكرة جديدة: 5', '2025-06-30 17:51:24'),
(35, 1, 'ticket_created', 'تم إنشاء تذكرة جديدة: 6', '2025-06-30 17:52:20'),
(36, 1, 'ticket_status_updated', 'تم تحديث حالة التذكرة: 6 إلى closed', '2025-06-30 17:52:30'),
(37, 1, 'ticket_comment_added', 'تم إضافة تعليق للتذكرة: 6', '2025-06-30 17:52:47'),
(38, 1, 'ticket_created', 'تم إنشاء تذكرة جديدة: 7', '2025-06-30 17:54:08'),
(39, 1, 'ticket_created', 'تم إنشاء تذكرة جديدة: 8', '2025-06-30 17:54:47'),
(40, 1, 'ticket_created', 'تم إنشاء تذكرة جديدة: 9', '2025-06-30 17:56:14'),
(41, 1, 'ticket_status_updated', 'تم تحديث حالة التذكرة: 9 إلى in_progress', '2025-06-30 17:57:02'),
(42, 1, 'ticket_status_updated', 'تم تحديث حالة التذكرة: 5 إلى in_review', '2025-06-30 17:57:25'),
(43, 1, 'ticket_status_updated', 'تم تحديث حالة التذكرة: 4 إلى in_review', '2025-06-30 17:57:38'),
(44, 1, 'ticket_status_updated', 'تم تحديث حالة التذكرة: 3 إلى in_review', '2025-06-30 17:57:50'),
(45, 1, 'ticket_status_updated', 'تم تحديث حالة التذكرة: 9 إلى in_review', '2025-06-30 17:58:02'),
(46, 1, 'ticket_status_updated', 'تم تحديث حالة التذكرة: 8 إلى in_review', '2025-06-30 17:58:24'),
(47, 1, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-06-30 18:00:26'),
(48, 14, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-06-30 18:01:41'),
(49, 1, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-06-30 18:59:23'),
(50, 1, 'ticket_status_updated', 'تم تحديث حالة التذكرة: 9 إلى in_review', '2025-07-01 15:13:37'),
(51, 1, 'ticket_status_updated', 'تم تحديث حالة التذكرة: 1 إلى in_progress', '2025-07-01 15:14:50'),
(52, 1, 'إضافة وكيل', 'تمت إضافة وكيل جديد: سويج النداء', '2025-07-01 15:24:28'),
(53, 1, 'إضافة نوع جهاز', 'تمت إضافة نوع جهاز جديد: SW-CRS-125', '2025-07-01 15:33:05'),
(54, 1, 'تعديل وكيل', 'تم تعديل بيانات الوكيل: سويج النداء (ID: 355)', '2025-07-01 15:33:43'),
(55, 1, 'ticket_status_updated', 'تم تحديث حالة التذكرة: 7 إلى in_review', '2025-07-02 08:03:39'),
(56, 1, 'ticket_status_updated', 'تم تحديث حالة التذكرة: 2 إلى in_review', '2025-07-02 08:03:50'),
(57, 1, 'ticket_status_updated', 'تم تحديث حالة التذكرة: 1 إلى in_review', '2025-07-02 08:04:47'),
(58, 1, 'ticket_status_updated', 'تم تحديث حالة التذكرة: 9 إلى resolved', '2025-07-02 08:45:09'),
(59, 1, 'ticket_status_updated', 'تم تحديث حالة التذكرة: 9 إلى closed', '2025-07-02 08:45:30'),
(60, 1, 'تعديل وكيل', 'تم تعديل بيانات الوكيل: محمد عباس الخيكاني (ID: 344)', '2025-07-02 16:56:35'),
(61, 1, 'تعديل وكيل', 'تم تعديل بيانات الوكيل: ماستر ربيتر عمود 79 (ID: 224)', '2025-07-02 18:28:19'),
(62, 1, 'تعديل وكيل', 'تم تعديل بيانات الوكيل: محمد عبد اليمه عزيز  (ID: 198)', '2025-07-02 18:51:55'),
(63, 1, 'إضافة وكيل', 'تمت إضافة وكيل جديد: قاسم محمد جواد', '2025-07-02 18:55:00'),
(64, 1, 'إضافة ربيتر', 'تمت إضافة ربيتر جديد: ربيتر الانصار-تابع الشركة', '2025-07-02 18:55:44'),
(65, 1, 'تعديل وكيل', 'تم تعديل بيانات الوكيل: قاسم محمد جواد (ID: 356)', '2025-07-02 18:57:10'),
(66, 1, 'إضافة وكيل', 'تمت إضافة وكيل جديد: قاسم محمد جواد', '2025-07-03 10:35:21'),
(67, 1, 'تعديل وكيل', 'تم تعديل بيانات الوكيل: علي عبد النبي (ID: 110)', '2025-07-03 14:45:11'),
(68, 1, 'تعديل وكيل', 'تم تعديل بيانات الوكيل: علي عبد النبي (ID: 110)', '2025-07-03 14:46:59'),
(69, 1, 'تعديل وكيل', 'تم تعديل بيانات الوكيل: قاسم محمد جواد (ID: 356)', '2025-07-03 18:20:58'),
(70, 1, 'تعديل وكيل', 'تم تعديل بيانات الوكيل: سجاد الابراهيمي 2 (ID: 160)', '2025-07-03 19:03:00'),
(71, 1, 'إضافة وكيل', 'تمت إضافة وكيل جديد: مكتب نجف-TOP-Office', '2025-07-05 14:52:54'),
(72, 1, 'تعديل وكيل', 'تم تعديل بيانات الوكيل: علاء يحيحه باقر  (ID: 106)', '2025-07-05 15:04:20'),
(73, 1, 'تعديل وكيل', 'تم تعديل بيانات الوكيل: امير حازم (ID: 215)', '2025-07-05 15:29:28'),
(74, 1, 'تعديل وكيل', 'تم تعديل بيانات الوكيل: حيدر حمزه سويد (ID: 52)', '2025-07-08 12:58:07'),
(75, 1, 'تعديل وكيل', 'تم تعديل بيانات الوكيل: قاسم محمد جواد (ID: 356)', '2025-07-08 18:46:09'),
(76, 1, 'تعديل وكيل', 'تم تعديل بيانات الوكيل: احمد فضل راجي (ID: 50)', '2025-07-09 11:40:07'),
(77, 1, 'تعديل وكيل', 'تم تعديل بيانات الوكيل: احمد فضل راجي (ID: 50)', '2025-07-09 19:05:20'),
(78, 1, 'ticket_status_updated', 'تم تحديث حالة التذكرة: 4 إلى closed', '2025-07-10 07:41:28'),
(79, 1, 'تعديل وكيل', 'تم تعديل بيانات الوكيل: مرتضى جبار حمزه1 (ID: 316)', '2025-07-10 14:42:45'),
(80, 1, 'تعديل وكيل', 'تم تعديل بيانات الوكيل: رضا محمد سرحان (ID: 209)', '2025-07-12 18:28:42'),
(81, 1, 'تعديل وكيل', 'تم تعديل بيانات الوكيل: امير حلاوة-امير جاسم محمد رضا (ID: 155)', '2025-07-13 07:33:14'),
(82, 1, 'ticket_created', 'تم إنشاء تذكرة جديدة: 10', '2025-07-13 12:51:29'),
(83, 1, 'ticket_status_updated', 'تم تحديث حالة التذكرة: 10 إلى in_review', '2025-07-13 12:51:39'),
(84, 1, 'ticket_status_updated', 'تم تحديث حالة التذكرة: 5 إلى closed', '2025-07-13 12:52:56'),
(85, 1, 'ticket_created', 'تم إنشاء تذكرة جديدة: 11', '2025-07-13 12:57:21'),
(86, 1, 'تعديل وكيل', 'تم تعديل بيانات الوكيل: ولاء عامر داخل محمد الرماحي (ID: 133)', '2025-07-13 12:58:58'),
(87, 1, 'تعديل وكيل', 'تم تعديل بيانات الوكيل: حسنين عماد عبدالاله (ID: 183)', '2025-07-13 14:32:37'),
(88, 1, 'ticket_status_updated', 'تم تحديث حالة التذكرة: 10 إلى closed', '2025-07-13 17:49:02'),
(89, 1, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-07-20 11:46:40'),
(90, 1, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-07-20 12:27:01'),
(91, 1, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-07-21 12:26:43'),
(92, 1, 'تحديث خيار تبعية', 'تم تحديث خيار تبعية: الشركة1', '2025-07-21 12:49:49'),
(93, 1, 'حذف خيار تبعية', 'تم حذف خيار تبعية: الشركة1', '2025-07-21 12:49:51'),
(94, 1, 'حذف خيار تبعية', 'تم حذف خيار تبعية: وكيل', '2025-07-21 12:49:53'),
(95, 1, 'حذف خيار تبعية', 'تم حذف خيار تبعية: فرع', '2025-07-21 12:49:54'),
(96, 1, 'حذف خيار تبعية', 'تم حذف خيار تبعية: شريك', '2025-07-21 12:49:56'),
(97, 1, 'حذف خيار تبعية', 'تم حذف خيار تبعية: مستقل', '2025-07-21 12:49:57'),
(98, 1, 'حذف مكان برج', 'تم حذف مكان برج: البرج الرئيسي', '2025-07-21 12:50:00'),
(99, 1, 'حذف مكان برج', 'تم حذف مكان برج: برج الكوفة', '2025-07-21 12:50:01'),
(100, 1, 'حذف مكان برج', 'تم حذف مكان برج: برج النجف القديمة', '2025-07-21 12:50:03'),
(101, 1, 'حذف مكان برج', 'تم حذف مكان برج: برج المنطقة الصناعية', '2025-07-21 12:50:04'),
(102, 1, 'حذف مكان برج', 'تم حذف مكان برج: برج الجامعة', '2025-07-21 12:50:06'),
(103, 1, 'حذف نوع خدمة', 'تم حذف نوع خدمة: إنترنت', '2025-07-21 12:50:09'),
(104, 1, 'حذف نوع خدمة', 'تم حذف نوع خدمة: واي فاي', '2025-07-21 12:50:10'),
(105, 1, 'حذف نوع خدمة', 'تم حذف نوع خدمة: فايبر', '2025-07-21 12:50:11'),
(106, 1, 'حذف نوع خدمة', 'تم حذف نوع خدمة: لاسلكي', '2025-07-21 12:50:13'),
(107, 1, 'حذف نوع خدمة', 'تم حذف نوع خدمة: مختلط', '2025-07-21 12:50:14'),
(108, 1, 'حذف مكان كابينة', 'تم حذف مكان كابينة: الكابينة الرئيسية', '2025-07-21 12:50:17'),
(109, 1, 'حذف مكان كابينة', 'تم حذف مكان كابينة: كابينة الكوفة', '2025-07-21 12:50:18'),
(110, 1, 'حذف مكان كابينة', 'تم حذف مكان كابينة: كابينة النجف القديمة', '2025-07-21 12:50:20'),
(111, 1, 'حذف مكان كابينة', 'تم حذف مكان كابينة: كابينة المنطقة الصناعية', '2025-07-21 12:50:21'),
(112, 1, 'حذف مكان كابينة', 'تم حذف مكان كابينة: كابينة الجامعة', '2025-07-21 12:50:23'),
(113, 1, 'حذف Port', 'تم حذف Port: Port 5', '2025-07-21 13:00:02'),
(114, 1, 'حذف Port', 'تم حذف Port: Port 4', '2025-07-21 13:00:03'),
(115, 1, 'حذف Port', 'تم حذف Port: Port 3', '2025-07-21 13:00:05'),
(116, 1, 'حذف Port', 'تم حذف Port: Port 2', '2025-07-21 13:00:06'),
(117, 1, 'حذف خصم', 'تم حذف خصم: خصم خاص', '2025-07-21 13:00:10'),
(118, 1, 'حذف خصم', 'تم حذف خصم: خصم 20%', '2025-07-21 13:00:12'),
(119, 1, 'حذف خصم', 'تم حذف خصم: خصم 15%', '2025-07-21 13:00:13'),
(120, 1, 'حذف خصم', 'تم حذف خصم: خصم 10%', '2025-07-21 13:00:15'),
(121, 1, 'حذف اسم فرع', 'تم حذف اسم فرع: فرع الجامعة', '2025-07-21 13:00:18'),
(122, 1, 'حذف اسم فرع', 'تم حذف اسم فرع: فرع المنطقة الصناعية', '2025-07-21 13:00:20'),
(123, 1, 'حذف اسم فرع', 'تم حذف اسم فرع: فرع النجف القديمة', '2025-07-21 13:00:22'),
(124, 1, 'حذف اسم فرع', 'تم حذف اسم فرع: فرع الكوفة', '2025-07-21 13:00:23'),
(125, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: عدنان نعمة', '2025-07-21 14:02:13'),
(126, 1, 'تحديث مكان كابينة', 'تم تحديث مكان كابينة: عدنان نعمة', '2025-07-21 14:02:23'),
(127, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: عباس باله', '2025-07-21 14:02:48'),
(128, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: عقيل رزاق', '2025-07-21 14:03:04'),
(129, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: سامي الحدراوي', '2025-07-21 14:03:14'),
(130, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: علي شذر', '2025-07-21 14:03:23'),
(131, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: عباس دايخ', '2025-07-21 14:03:32'),
(132, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: عقيل صبيح', '2025-07-21 14:03:40'),
(133, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: علاء حامد', '2025-07-21 14:03:59'),
(134, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: سجاد ابن الشيخ', '2025-07-21 14:04:10'),
(135, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: علي عبد مسلم', '2025-07-21 14:04:21'),
(136, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: رضا المضمد', '2025-07-21 14:04:32'),
(137, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: علي وزير', '2025-07-21 14:04:42'),
(138, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: جور مولدة ابو مالك على التيل', '2025-07-21 14:04:51'),
(139, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: علي مزهر', '2025-07-21 14:05:00'),
(140, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: عبدعلي ذياب', '2025-07-21 14:05:06'),
(141, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: عدنان العيساوي', '2025-07-21 14:05:14'),
(142, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: عامر فليفل', '2025-07-21 14:05:25'),
(143, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: مسلم وثيق', '2025-07-21 14:06:04'),
(144, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: مقابيل ابو زهراء اقصاد', '2025-07-21 14:06:16'),
(145, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: رزاق لفته', '2025-07-21 14:06:28'),
(146, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: علي رزاق', '2025-07-21 14:06:36'),
(147, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: بشارع الي يم المدرسه', '2025-07-21 14:07:00'),
(148, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: بالدربونه الي يم المدرسه', '2025-07-21 14:07:09'),
(149, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: علي التيل جوار بيت ابو حاتم', '2025-07-21 14:07:45'),
(150, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: يم بيت مرزوقي', '2025-07-21 14:07:56'),
(151, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: يم مولدة سجاد سلام', '2025-07-21 14:08:03'),
(152, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: يم علي فاضل', '2025-07-21 14:08:10'),
(153, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: ابراهيم انشائيه', '2025-07-21 14:08:19'),
(154, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: دربونه ادريس', '2025-07-21 14:08:34'),
(155, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: يم مهند ابو الملابس', '2025-07-21 14:08:42'),
(156, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: جوار صاله زيد ابوالاتاري', '2025-07-21 14:08:54'),
(157, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: يم مسلم ابو التنانير', '2025-07-21 14:09:01'),
(158, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: فارس الكريطي', '2025-07-21 14:09:07'),
(159, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: تحسين الحلاق', '2025-07-21 14:09:15'),
(160, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: محمد الحمراني', '2025-07-21 14:09:25'),
(161, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: سيد علي الحلاق', '2025-07-21 14:09:33'),
(162, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: فوق حيدر اتاري بسطح', '2025-07-21 14:09:41'),
(163, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: رضا مطرود', '2025-07-21 14:09:51'),
(164, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: سيد غسان', '2025-07-21 14:09:59'),
(165, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: طالب رحيم', '2025-07-21 14:10:06'),
(166, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: كابينة الشقة', '2025-07-21 14:10:12'),
(167, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: كابينه دكتور ماجد', '2025-07-21 14:10:20'),
(168, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: كابينه مولدة ابن عناد', '2025-07-21 14:10:27'),
(169, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: مصطفى جوار فرقان', '2025-07-21 14:10:37'),
(170, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: سجاد حرب', '2025-07-21 14:10:57'),
(171, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: بيت سيد ثامر', '2025-07-21 14:11:06'),
(172, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: كابينه مقابيل بيت باله على البيت', '2025-07-21 14:11:20'),
(173, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: ازياء سيد حمودي ', '2025-07-21 14:11:26'),
(174, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: حيدر ابو الحجامه', '2025-07-21 14:11:34'),
(175, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: فوك دائرة الكهرباء', '2025-07-21 14:11:42'),
(176, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: FAT1', '2025-07-21 14:12:07'),
(177, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: FAT2', '2025-07-21 14:12:19'),
(178, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: FAT3', '2025-07-21 14:12:32'),
(179, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: FAT4', '2025-07-21 14:12:39'),
(180, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: FAT5', '2025-07-21 14:12:47'),
(181, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: FAT6', '2025-07-21 14:12:53'),
(182, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: FAT7', '2025-07-21 14:12:58'),
(183, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: FAT8', '2025-07-21 14:13:03'),
(184, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: FAT9', '2025-07-21 14:13:09'),
(185, 1, 'إضافة مكان كابينة', 'تمت إضافة مكان كابينة جديد: FAT10', '2025-07-21 14:13:15'),
(186, 1, 'حذف Port', 'تم حذف Port: Port 1', '2025-07-21 14:13:52'),
(187, 1, 'إضافة Port', 'تمت إضافة Port جديد: P1', '2025-07-21 14:14:02'),
(188, 1, 'إضافة Port', 'تمت إضافة Port جديد: P2', '2025-07-21 14:14:11'),
(189, 1, 'إضافة Port', 'تمت إضافة Port جديد: P3', '2025-07-21 14:14:17'),
(190, 1, 'إضافة Port', 'تمت إضافة Port جديد: P4', '2025-07-21 14:14:24'),
(191, 1, 'إضافة Port', 'تمت إضافة Port جديد: P5', '2025-07-21 14:14:31'),
(192, 1, 'إضافة Port', 'تمت إضافة Port جديد: P6', '2025-07-21 14:14:37'),
(193, 1, 'إضافة Port', 'تمت إضافة Port جديد: P7', '2025-07-21 14:14:43'),
(194, 1, 'إضافة Port', 'تمت إضافة Port جديد: P8', '2025-07-21 14:14:50'),
(195, 1, 'إضافة Port', 'تمت إضافة Port جديد: P9', '2025-07-21 14:14:57'),
(196, 1, 'إضافة Port', 'تمت إضافة Port جديد: P10', '2025-07-21 14:15:02'),
(197, 1, 'إضافة Port', 'تمت إضافة Port جديد: P11', '2025-07-21 14:15:08'),
(198, 1, 'إضافة Port', 'تمت إضافة Port جديد: P12', '2025-07-21 14:15:15'),
(199, 1, 'إضافة Port', 'تمت إضافة Port جديد: P13', '2025-07-21 14:15:23'),
(200, 1, 'إضافة Port', 'تمت إضافة Port جديد: P14', '2025-07-21 14:15:29'),
(201, 1, 'إضافة Port', 'تمت إضافة Port جديد: P15', '2025-07-21 14:15:34'),
(202, 1, 'إضافة Port', 'تمت إضافة Port جديد: P16', '2025-07-21 14:15:40'),
(203, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: المكتب الرئيسي', '2025-07-21 14:18:01'),
(204, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: مكتب اكس', '2025-07-21 14:18:22'),
(205, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: مكتب الولاية', '2025-07-21 14:18:30'),
(206, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: مكتب البراكية', '2025-07-21 14:18:37'),
(207, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: مكتب سجاد سعيد', '2025-07-21 14:18:45'),
(208, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: مكتب نجف اونلاين', '2025-07-21 14:18:55'),
(209, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: مكتب مجمع المختار', '2025-07-21 14:19:07'),
(210, 1, 'حذف نوع خدمة', 'تم حذف نوع خدمة: المكتب الرئيسي', '2025-07-21 14:20:00'),
(211, 1, 'حذف نوع خدمة', 'تم حذف نوع خدمة: مكتب اكس', '2025-07-21 14:20:02'),
(212, 1, 'حذف نوع خدمة', 'تم حذف نوع خدمة: مكتب مجمع المختار', '2025-07-21 14:20:03'),
(213, 1, 'حذف نوع خدمة', 'تم حذف نوع خدمة: مكتب نجف اونلاين', '2025-07-21 14:20:04'),
(214, 1, 'حذف نوع خدمة', 'تم حذف نوع خدمة: مكتب سجاد سعيد', '2025-07-21 14:20:05'),
(215, 1, 'حذف نوع خدمة', 'تم حذف نوع خدمة: مكتب البراكية', '2025-07-21 14:20:08'),
(216, 1, 'حذف نوع خدمة', 'تم حذف نوع خدمة: مكتب الولاية', '2025-07-21 14:20:09'),
(217, 1, 'تحديث مكان كابينة', 'تم تحديث مكان كابينة: ازياء سيد حمودي ', '2025-07-21 14:22:45'),
(218, 1, 'تحديث مكان كابينة', 'تم تحديث مكان كابينة: كابينه مقابيل بيت باله على البيت', '2025-07-21 14:22:52'),
(219, 1, 'تحديث مكان كابينة', 'تم تحديث مكان كابينة: عدنان نعمة1', '2025-07-21 14:27:07'),
(220, 1, 'تحديث مكان كابينة', 'تم تحديث مكان كابينة: عدنان نعمة', '2025-07-21 14:27:15'),
(221, 1, 'حذف اسم فرع', 'تم حذف اسم فرع: الفرع الرئيسي', '2025-07-21 14:27:50'),
(222, 1, 'إضافة اسم فرع', 'تمت إضافة اسم فرع جديد: المكتب الرئيسي', '2025-07-21 14:27:59'),
(223, 1, 'إضافة اسم فرع', 'تمت إضافة اسم فرع جديد: مكتب اكس', '2025-07-21 14:28:07'),
(224, 1, 'إضافة اسم فرع', 'تمت إضافة اسم فرع جديد: مكتب الولاية', '2025-07-21 14:28:15'),
(225, 1, 'إضافة اسم فرع', 'تمت إضافة اسم فرع جديد: مكتب البراكية', '2025-07-21 14:28:26'),
(226, 1, 'إضافة اسم فرع', 'تمت إضافة اسم فرع جديد: مكتب سجاد سعيد', '2025-07-21 14:28:34'),
(227, 1, 'إضافة اسم فرع', 'تمت إضافة اسم فرع جديد: مكتب نجف اونلاين', '2025-07-21 14:28:42'),
(228, 1, 'إضافة اسم فرع', 'تمت إضافة اسم فرع جديد: مكتب مجمع المختار', '2025-07-21 14:28:50'),
(229, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: ايرث لنك', '2025-07-21 14:29:41'),
(230, 1, 'تحديث نوع خدمة', 'تم تحديث نوع خدمة: ايرث لنك', '2025-07-21 14:29:46'),
(231, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: NajafOnline25', '2025-07-21 14:29:54'),
(232, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: Topnajaf1', '2025-07-21 14:30:03'),
(233, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: NajafOnline126', '2025-07-21 14:30:09'),
(234, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: سبيد لنك برود باند', '2025-07-21 14:30:19'),
(235, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: NajafOnline19', '2025-07-21 14:30:26'),
(236, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: NajafOnline12', '2025-07-21 14:30:33'),
(237, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: اول تيليكوم', '2025-07-21 14:30:46'),
(238, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: TL-n20', '2025-07-21 14:30:57'),
(239, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: TL-n28', '2025-07-21 14:31:08'),
(240, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: FTTH-174', '2025-07-21 14:31:16'),
(241, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: speedlink', '2025-07-21 14:31:24'),
(242, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: NajafOnline', '2025-07-21 14:31:38'),
(243, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: NajafOnline30', '2025-07-21 14:31:44'),
(244, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: tlnn1', '2025-07-21 14:31:50'),
(245, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: tln5', '2025-07-21 14:31:56'),
(246, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: NajafOnline3', '2025-07-21 14:32:05'),
(247, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: NajafOnline4', '2025-07-21 14:32:13'),
(248, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: NajafOnline10', '2025-07-21 14:32:20'),
(249, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: الوطني', '2025-07-21 14:32:26'),
(250, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: NajafOnline26', '2025-07-21 14:32:32'),
(251, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: NajafOnline6', '2025-07-21 14:32:38'),
(252, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: tln6', '2025-07-21 14:32:44'),
(253, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: tln9', '2025-07-21 14:32:50'),
(254, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: NajafOnline64', '2025-07-21 14:32:56'),
(255, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: TOP5', '2025-07-21 14:33:02'),
(256, 1, 'إضافة نوع خدمة', 'تمت إضافة نوع خدمة جديد: tln10', '2025-07-21 14:33:09'),
(257, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: سبيد لنك الكوفة', '2025-07-21 14:34:02'),
(258, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: مكتب x', '2025-07-21 14:34:12'),
(259, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: عقيل الفتلاوي', '2025-07-21 14:34:21'),
(260, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: فاضل الشعبي', '2025-07-21 14:34:29'),
(261, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: كرار سعد', '2025-07-21 14:37:39'),
(262, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: مصطفى حسن', '2025-07-21 14:38:14'),
(263, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: مصطفى خضير', '2025-07-21 14:38:23'),
(264, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: عقيل رزاق', '2025-07-21 14:38:30'),
(265, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: سيف احسان', '2025-07-21 14:38:38'),
(266, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: برج الجمهورية', '2025-07-21 14:38:49'),
(267, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: برج فيض الكوفة', '2025-07-21 14:39:00'),
(268, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: برج العلوة', '2025-07-21 14:39:07'),
(269, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: عبد الحسين الخباز', '2025-07-21 14:39:14'),
(270, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: برج زيد ميسان', '2025-07-21 14:39:21'),
(271, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: برج السكة', '2025-07-21 14:39:28'),
(272, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: البرج الرئيسي-ضوئي', '2025-07-21 14:39:42'),
(273, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: برج ياسين', '2025-07-21 14:39:51'),
(274, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: برج فندق ام القرى', '2025-07-21 14:39:59'),
(275, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: هشام الفتلاوي البيت', '2025-07-21 14:40:26'),
(276, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: مكتب البراكية', '2025-07-21 14:40:35'),
(277, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: فندق وردة النجف', '2025-07-21 14:40:43'),
(278, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: الاسكان واير لنك', '2025-07-21 14:40:51'),
(279, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: جامعة الصادق', '2025-07-21 14:40:59'),
(280, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: الملعب', '2025-07-21 14:41:07'),
(281, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: مجمع المختار', '2025-07-21 14:41:14'),
(282, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: ربيتر الميلاد', '2025-07-21 14:41:21'),
(283, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: فندق قصر المولى', '2025-07-21 14:41:28'),
(284, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: الجنسية علي سمارت', '2025-07-21 14:41:36'),
(285, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: فندق جنة علي', '2025-07-21 14:41:43'),
(286, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: فندق العطاء', '2025-07-21 14:41:51'),
(287, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: البراكية2-BR814', '2025-07-21 14:41:59'),
(288, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: اسواق الكرادة الشرقية', '2025-07-21 14:42:06'),
(289, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: مكتب سيد علاء', '2025-07-21 14:42:20'),
(290, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: مكتب نجف اونلاين', '2025-07-21 14:42:27'),
(291, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: سبيد ماكس', '2025-07-21 14:44:37'),
(292, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: مكتب الشرق-غازي', '2025-07-21 14:44:45'),
(293, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: اسواق زيد ابن علي', '2025-07-21 14:44:54'),
(294, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: ربيتر النداء', '2025-07-21 14:45:02'),
(295, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: كوفي ليالي الفرات', '2025-07-21 14:45:13'),
(296, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: برج منتظر قاسم', '2025-07-21 14:45:21'),
(297, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: الحسيسنة عمود79', '2025-07-21 14:45:28'),
(298, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: برج بيت زيد جواد', '2025-07-21 14:46:20'),
(299, 1, 'إضافة مكان برج', 'تمت إضافة مكان برج جديد: ربيتر الانصار', '2025-07-21 14:46:33'),
(300, 1, 'إضافة خيار تبعية', 'تمت إضافة خيار تبعية جديد: سبيد لنك', '2025-07-21 14:46:57'),
(301, 1, 'إضافة خيار تبعية', 'تمت إضافة خيار تبعية جديد: سجاد سعيد', '2025-07-21 14:47:05'),
(302, 1, 'إضافة خيار تبعية', 'تمت إضافة خيار تبعية جديد: علي شاكر', '2025-07-21 14:47:14'),
(303, 1, 'إضافة خيار تبعية', 'تمت إضافة خيار تبعية جديد: علي هادي', '2025-07-21 14:47:23'),
(304, 1, 'إضافة خيار تبعية', 'تمت إضافة خيار تبعية جديد: مسلم سعد', '2025-07-21 14:47:37'),
(305, 1, 'إضافة خيار تبعية', 'تمت إضافة خيار تبعية جديد: عقيل رزاق', '2025-07-21 14:47:45'),
(306, 1, 'إضافة خيار تبعية', 'تمت إضافة خيار تبعية جديد: كرار حيدر', '2025-07-21 14:47:53'),
(307, 1, 'إضافة خيار تبعية', 'تمت إضافة خيار تبعية جديد: SPEED-MAX', '2025-07-21 14:48:06'),
(308, 1, 'إضافة خيار تبعية', 'تمت إضافة خيار تبعية جديد: سيف احسان', '2025-07-21 14:48:13'),
(309, 1, 'إضافة خيار تبعية', 'تمت إضافة خيار تبعية جديد: ليث عبدالجليل', '2025-07-21 14:48:22'),
(310, 1, 'إضافة خيار تبعية', 'تمت إضافة خيار تبعية جديد: ياسين عبدالامير', '2025-07-21 14:48:29'),
(311, 1, 'إضافة خيار تبعية', 'تمت إضافة خيار تبعية جديد: البراكية', '2025-07-21 14:48:36'),
(312, 1, 'إضافة خيار تبعية', 'تمت إضافة خيار تبعية جديد: جامعة الصادق', '2025-07-21 14:48:49'),
(313, 1, 'إضافة خيار تبعية', 'تمت إضافة خيار تبعية جديد: وايرلنك', '2025-07-21 14:48:57'),
(314, 1, 'إضافة خيار تبعية', 'تمت إضافة خيار تبعية جديد: امير سعدون', '2025-07-21 14:49:06'),
(315, 1, 'إضافة خيار تبعية', 'تمت إضافة خيار تبعية جديد: علي اسماعيل', '2025-07-21 14:49:13'),
(316, 1, 'إضافة خيار تبعية', 'تمت إضافة خيار تبعية جديد: الولاية', '2025-07-21 14:49:19'),
(317, 1, 'إضافة خيار تبعية', 'تمت إضافة خيار تبعية جديد: SPEED-X', '2025-07-21 14:49:36'),
(318, 1, 'إضافة خيار تبعية', 'تمت إضافة خيار تبعية جديد: علي جواد', '2025-07-21 14:49:45'),
(319, 1, 'إضافة خيار تبعية', 'تمت إضافة خيار تبعية جديد: نجف اونلاين', '2025-07-21 14:50:07'),
(320, 1, 'إضافة خيار تبعية', 'تمت إضافة خيار تبعية جديد: منتظر قاسم', '2025-07-21 14:50:15'),
(321, 1, 'إضافة خيار تبعية', 'تمت إضافة خيار تبعية جديد: زيد جواد', '2025-07-21 14:50:24'),
(322, 1, 'إضافة خيار تبعية', 'تمت إضافة خيار تبعية جديد: مجمع المختار', '2025-07-21 14:50:35'),
(323, 1, 'حذف خصم', 'تم حذف خصم: خصم 5%', '2025-07-21 15:00:39'),
(324, 1, 'إضافة خصم', 'تمت إضافة خصم جديد: VIP', '2025-07-21 15:00:42'),
(325, 1, 'إضافة خصم', 'تمت إضافة خصم جديد: ذهبي', '2025-07-21 15:00:50'),
(326, 1, 'إضافة خصم', 'تمت إضافة خصم جديد: فضي', '2025-07-21 15:00:56'),
(327, 1, 'إضافة خصم', 'تمت إضافة خصم جديد: مجاني', '2025-07-21 15:01:02'),
(328, 1, 'إضافة خصم', 'تمت إضافة خصم جديد: خاص', '2025-07-21 15:01:08'),
(329, 1, 'إضافة خصم', 'تمت إضافة خصم جديد: فواتير', '2025-07-21 15:01:13'),
(330, 1, 'إضافة وكيل', 'تمت إضافة مشترك جديد: محمد فارس عطيه بلا الحداد', '2025-07-21 15:42:39'),
(331, 1, 'تعديل مشترك', 'تم تعديل بيانات المشترك: محمد فارس عطيه بلا الحداد (ID: 359)', '2025-07-21 16:02:34'),
(332, 1, 'تعديل مشترك', 'تم تعديل بيانات المشترك: محمد فارس عطيه بلا الحداد (ID: 359)', '2025-07-21 16:08:06'),
(333, 1, 'تعديل مشترك', 'تم تعديل بيانات المشترك: محمد فارس عطيه بلا الحداد (ID: 359)', '2025-07-21 16:08:53'),
(334, 1, 'تعديل مشترك', 'تم تعديل بيانات المشترك: محمد فارس عطيه بلا الحداد (ID: 359)', '2025-07-21 16:21:30'),
(335, 1, 'تعديل مشترك', 'تم تعديل بيانات المشترك: محمد فارس عطيه بلا الحداد (ID: 359)', '2025-07-21 16:21:54'),
(336, 1, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-07-21 16:53:46'),
(337, 1, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-07-21 18:01:47'),
(338, 1, 'تسجيل خروج', 'تم تسجيل الخروج من النظام', '2025-07-21 18:44:47'),
(339, 1, 'إضافة وكيل', 'تمت إضافة مشترك جديد: اركان ماجد عبد زيد', '2025-07-21 18:45:38'),
(340, 1, 'إضافة وكيل', 'تمت إضافة مشترك جديد: فندق القمر', '2025-07-22 11:12:26'),
(341, 1, 'نسخ احتياطي', 'تم إنشاء نسخة احتياطية جديدة: backup_2025-07-22_11-19-16.sql', '2025-07-22 11:19:16');

-- --------------------------------------------------------

--
-- Table structure for table `admins`
--

CREATE TABLE `admins` (
  `id` int NOT NULL,
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `agents`
--

CREATE TABLE `agents` (
  `id` int NOT NULL,
  `agent_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone_number` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `secondary_phone_number` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `repeater_id` int DEFAULT NULL,
  `device_type_id` int DEFAULT NULL,
  `job_title` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `point_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `username` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `device_function` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `rp_sub` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `switch_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `port` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `device_ownership` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `unms_status` enum('active','inactive') COLLATE utf8mb4_unicode_ci DEFAULT 'active',
  `notes` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `latitude` decimal(10,8) DEFAULT NULL,
  `longitude` decimal(11,8) DEFAULT NULL,
  `coverage_radius` int DEFAULT NULL COMMENT 'نطاق التغطية بالأمتار',
  `last_status` enum('online','offline') COLLATE utf8mb4_unicode_ci DEFAULT 'offline',
  `last_status_change` datetime DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `mac_address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ssid` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `serial_number` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'تسلسل',
  `sn_onu` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'SN ONU',
  `sector_number` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'رقم السكتر',
  `bill_price` decimal(10,2) DEFAULT NULL COMMENT 'سعر فواتير',
  `port_id` int DEFAULT NULL COMMENT 'Port',
  `cabinet_location_id` int DEFAULT NULL COMMENT 'مكان كابينة',
  `discount_id` int DEFAULT NULL COMMENT 'الخصم',
  `service_type_id` int DEFAULT NULL COMMENT 'نوع الخدمة',
  `belongs_to_id` int DEFAULT NULL COMMENT 'تابع الى',
  `branch_name_id` int DEFAULT NULL COMMENT 'اسم الفرع',
  `tower_location_id` int DEFAULT NULL COMMENT 'مكان البرج'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `agents`
--

INSERT INTO `agents` (`id`, `agent_name`, `phone_number`, `secondary_phone_number`, `repeater_id`, `device_type_id`, `job_title`, `point_name`, `ip_address`, `username`, `password`, `device_function`, `rp_sub`, `switch_name`, `port`, `device_ownership`, `unms_status`, `notes`, `created_at`, `latitude`, `longitude`, `coverage_radius`, `last_status`, `last_status_change`, `is_active`, `mac_address`, `ssid`, `serial_number`, `sn_onu`, `sector_number`, `bill_price`, `port_id`, `cabinet_location_id`, `discount_id`, `service_type_id`, `belongs_to_id`, `branch_name_id`, `tower_location_id`) VALUES
(1, 'فندق القمر', '07714403444', '', NULL, NULL, NULL, 'NajafOnline10', NULL, '667@NajafOnline10', '1234', NULL, '', NULL, '', 'سبيد لنك', 'active', '', '2025-07-22 11:12:26', NULL, NULL, NULL, 'offline', NULL, 1, '', '', '667', '', 'BR855', 33750.00, NULL, NULL, 11, 31, 22, 8, 34);

-- --------------------------------------------------------

--
-- Table structure for table `agent_connection_logs`
--

CREATE TABLE `agent_connection_logs` (
  `id` int NOT NULL,
  `agent_id` int NOT NULL,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci NOT NULL,
  `status` enum('connected','disconnected') CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci NOT NULL,
  `timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_icelandic_ci;

-- --------------------------------------------------------

--
-- Table structure for table `agent_ips`
--

CREATE TABLE `agent_ips` (
  `id` int NOT NULL,
  `agent_id` int NOT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL,
  `ip_description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `username` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `password` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_primary` tinyint(1) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_ping_status` tinyint(1) DEFAULT NULL,
  `last_ping_time` datetime DEFAULT NULL,
  `last_successful_ping` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `agent_snmp_settings`
--

CREATE TABLE `agent_snmp_settings` (
  `id` int NOT NULL,
  `agent_id` int NOT NULL,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `snmp_community` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'public',
  `snmp_version` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '2c',
  `device_type` enum('mikrotik','cisco','ubiquiti','other') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'mikrotik',
  `is_enabled` tinyint(1) DEFAULT '1',
  `last_check` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `agent_status_logs`
--

CREATE TABLE `agent_status_logs` (
  `id` int NOT NULL,
  `agent_id` int NOT NULL,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci NOT NULL,
  `old_status` enum('online','offline') CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci NOT NULL,
  `new_status` enum('online','offline') CHARACTER SET utf8mb4 COLLATE utf8mb4_icelandic_ci NOT NULL,
  `response_time` int DEFAULT NULL,
  `changed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_icelandic_ci;

-- --------------------------------------------------------

--
-- Table structure for table `agent_uptime_stats`
--

CREATE TABLE `agent_uptime_stats` (
  `id` int NOT NULL,
  `agent_id` int NOT NULL,
  `date_recorded` date NOT NULL,
  `total_checks` int DEFAULT '0',
  `successful_checks` int DEFAULT '0',
  `total_downtime` int DEFAULT '0',
  `average_response_time` int DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_icelandic_ci;

-- --------------------------------------------------------

--
-- Table structure for table `backups`
--

CREATE TABLE `backups` (
  `id` int NOT NULL,
  `filename` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_by` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `backup_settings`
--

CREATE TABLE `backup_settings` (
  `id` int NOT NULL,
  `frequency` enum('daily','weekly','monthly') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `time` time NOT NULL,
  `retention_days` int NOT NULL DEFAULT '30',
  `last_backup` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `backup_settings`
--

INSERT INTO `backup_settings` (`id`, `frequency`, `time`, `retention_days`, `last_backup`, `created_at`) VALUES
(1, 'daily', '00:00:00', 30, '2024-12-04 13:13:05', '2024-12-04 10:10:50'),
(2, 'weekly', '00:00:00', 30, NULL, '2024-12-04 10:13:26'),
(3, 'weekly', '00:00:00', 30, '2024-12-11 18:52:46', '2024-12-04 10:13:32'),
(4, 'daily', '00:00:00', 30, '2025-04-26 00:00:01', '2024-12-19 11:41:56'),
(5, 'daily', '00:00:00', 30, '2025-04-27 00:00:01', '2025-04-26 14:26:28'),
(6, 'daily', '00:00:00', 30, '2025-05-28 00:00:01', '2025-04-27 17:52:31'),
(7, 'daily', '00:00:00', 30, '2025-06-25 00:00:02', '2025-05-28 07:43:14'),
(8, 'daily', '00:00:00', 30, NULL, '2025-06-25 14:31:12'),
(9, 'daily', '00:00:00', 30, '2025-06-28 17:54:42', '2025-06-25 14:31:34'),
(10, 'daily', '00:00:00', 30, NULL, '2025-06-30 15:42:22'),
(11, 'daily', '00:00:00', 30, '2025-07-14 00:00:02', '2025-06-30 16:40:47');

-- --------------------------------------------------------

--
-- Table structure for table `belongs_to_options`
--

CREATE TABLE `belongs_to_options` (
  `id` int NOT NULL,
  `name` varchar(255) NOT NULL,
  `code` varchar(50) NOT NULL,
  `description` text,
  `contact_person` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `address` varchar(500) DEFAULT NULL,
  `contract_start` date DEFAULT NULL,
  `contract_end` date DEFAULT NULL,
  `commission_rate` decimal(5,2) DEFAULT NULL,
  `status` enum('active','inactive','suspended') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `belongs_to_options`
--

INSERT INTO `belongs_to_options` (`id`, `name`, `code`, `description`, `contact_person`, `phone`, `email`, `address`, `contract_start`, `contract_end`, `commission_rate`, `status`, `created_at`, `updated_at`) VALUES
(6, 'سبيد لنك', '_1753109217', 'سبيد لنك', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:46:57', '2025-07-21 14:46:57'),
(7, 'سجاد سعيد', '_1753109225', 'سجاد سعيد', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:47:05', '2025-07-21 14:47:05'),
(8, 'علي شاكر', '_1753109234', 'علي شاكر', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:47:14', '2025-07-21 14:47:14'),
(9, 'علي هادي', '_1753109243', 'علي هادي', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:47:23', '2025-07-21 14:47:23'),
(10, 'مسلم سعد', '_1753109257', 'مسلم سعد', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:47:37', '2025-07-21 14:47:37'),
(11, 'عقيل رزاق', '_1753109265', 'عقيل رزاق', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:47:45', '2025-07-21 14:47:45'),
(12, 'كرار حيدر', '_1753109273', 'كرار حيدر', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:47:53', '2025-07-21 14:47:53'),
(13, 'SPEED-MAX', 'SPEEDMAX_1753109286', 'SPEED-MAX', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:48:06', '2025-07-21 14:48:06'),
(14, 'سيف احسان', '_1753109293', 'سيف احسان', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:48:13', '2025-07-21 14:48:13'),
(15, 'ليث عبدالجليل', '_1753109302', 'ليث عبدالجليل', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:48:22', '2025-07-21 14:48:22'),
(16, 'ياسين عبدالامير', '_1753109309', 'ياسين عبدالامير', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:48:29', '2025-07-21 14:48:29'),
(17, 'البراكية', '_1753109316', 'البراكية', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:48:36', '2025-07-21 14:48:36'),
(18, 'جامعة الصادق', '_1753109329', 'جامعة الصادق', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:48:49', '2025-07-21 14:48:49'),
(19, 'وايرلنك', '_1753109337', 'وايرلنك', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:48:57', '2025-07-21 14:48:57'),
(20, 'امير سعدون', '_1753109346', 'امير سعدون', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:49:06', '2025-07-21 14:49:06'),
(21, 'علي اسماعيل', '_1753109353', 'علي اسماعيل', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:49:13', '2025-07-21 14:49:13'),
(22, 'الولاية', '_1753109359', 'الولاية', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:49:19', '2025-07-21 14:49:19'),
(23, 'SPEED-X', 'SPEEDX_1753109376', 'SPEED-X', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:49:36', '2025-07-21 14:49:36'),
(24, 'علي جواد', '_1753109385', 'علي جواد', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:49:45', '2025-07-21 14:49:45'),
(25, 'نجف اونلاين', '_1753109407', 'نجف اونلاين', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:50:07', '2025-07-21 14:50:07'),
(26, 'منتظر قاسم', '_1753109415', 'منتظر قاسم', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:50:15', '2025-07-21 14:50:15'),
(27, 'زيد جواد', '_1753109424', 'زيد جواد', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:50:24', '2025-07-21 14:50:24'),
(28, 'مجمع المختار', '_1753109435', 'مجمع المختار', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:50:35', '2025-07-21 14:50:35');

-- --------------------------------------------------------

--
-- Table structure for table `branch_names`
--

CREATE TABLE `branch_names` (
  `id` int NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `branch_names`
--

INSERT INTO `branch_names` (`id`, `name`, `description`, `status`, `created_at`, `updated_at`) VALUES
(6, 'المكتب الرئيسي', 'المكتب الرئيسي', 'active', '2025-07-21 14:27:59', '2025-07-21 14:27:59'),
(7, 'مكتب اكس', 'مكتب اكس', 'active', '2025-07-21 14:28:07', '2025-07-21 14:28:07'),
(8, 'مكتب الولاية', 'مكتب الولاية', 'active', '2025-07-21 14:28:15', '2025-07-21 14:28:15'),
(9, 'مكتب البراكية', 'مكتب البراكية\\r\\n', 'active', '2025-07-21 14:28:26', '2025-07-21 14:28:26'),
(10, 'مكتب سجاد سعيد', 'مكتب سجاد سعيد', 'active', '2025-07-21 14:28:34', '2025-07-21 14:28:34'),
(11, 'مكتب نجف اونلاين', 'مكتب نجف اونلاين', 'active', '2025-07-21 14:28:42', '2025-07-21 14:28:42'),
(12, 'مكتب مجمع المختار', 'مكتب مجمع المختار', 'active', '2025-07-21 14:28:50', '2025-07-21 14:28:50');

-- --------------------------------------------------------

--
-- Table structure for table `cabinet_locations`
--

CREATE TABLE `cabinet_locations` (
  `id` int NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `address` varchar(500) DEFAULT NULL,
  `latitude` decimal(10,8) DEFAULT NULL,
  `longitude` decimal(11,8) DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `cabinet_locations`
--

INSERT INTO `cabinet_locations` (`id`, `name`, `description`, `address`, `latitude`, `longitude`, `status`, `created_at`, `updated_at`) VALUES
(6, 'عدنان نعمة', 'عدنان نعمة', NULL, NULL, NULL, 'active', '2025-07-21 14:02:13', '2025-07-21 14:27:15'),
(7, 'عباس باله', 'عباس باله', NULL, NULL, NULL, 'active', '2025-07-21 14:02:48', '2025-07-21 14:02:48'),
(8, 'عقيل رزاق', 'عقيل رزاق', NULL, NULL, NULL, 'active', '2025-07-21 14:03:04', '2025-07-21 14:03:04'),
(9, 'سامي الحدراوي', 'سامي الحدراوي', NULL, NULL, NULL, 'active', '2025-07-21 14:03:14', '2025-07-21 14:03:14'),
(10, 'علي شذر', 'علي شذر', NULL, NULL, NULL, 'active', '2025-07-21 14:03:23', '2025-07-21 14:03:23'),
(11, 'عباس دايخ', 'عباس دايخ', NULL, NULL, NULL, 'active', '2025-07-21 14:03:32', '2025-07-21 14:03:32'),
(12, 'عقيل صبيح', 'عقيل صبيح', NULL, NULL, NULL, 'active', '2025-07-21 14:03:40', '2025-07-21 14:03:40'),
(13, 'علاء حامد', 'علاء حامد', NULL, NULL, NULL, 'active', '2025-07-21 14:03:59', '2025-07-21 14:03:59'),
(14, 'سجاد ابن الشيخ', 'سجاد ابن الشيخ', NULL, NULL, NULL, 'active', '2025-07-21 14:04:10', '2025-07-21 14:04:10'),
(15, 'علي عبد مسلم', 'علي عبد مسلم', NULL, NULL, NULL, 'active', '2025-07-21 14:04:21', '2025-07-21 14:04:21'),
(16, 'رضا المضمد', 'رضا المضمد', NULL, NULL, NULL, 'active', '2025-07-21 14:04:32', '2025-07-21 14:04:32'),
(17, 'علي وزير', 'علي وزير', NULL, NULL, NULL, 'active', '2025-07-21 14:04:42', '2025-07-21 14:04:42'),
(18, 'جور مولدة ابو مالك على التيل', 'جور مولدة ابو مالك على التيل', NULL, NULL, NULL, 'active', '2025-07-21 14:04:51', '2025-07-21 14:04:51'),
(19, 'علي مزهر', 'علي مزهر', NULL, NULL, NULL, 'active', '2025-07-21 14:05:00', '2025-07-21 14:05:00'),
(20, 'عبدعلي ذياب', 'عبدعلي ذياب', NULL, NULL, NULL, 'active', '2025-07-21 14:05:06', '2025-07-21 14:05:06'),
(21, 'عدنان العيساوي', 'عدنان العيساوي', NULL, NULL, NULL, 'active', '2025-07-21 14:05:14', '2025-07-21 14:05:14'),
(22, 'عامر فليفل', 'عامر فليفل', NULL, NULL, NULL, 'active', '2025-07-21 14:05:25', '2025-07-21 14:05:25'),
(24, 'مسلم وثيق', 'مسلم وثيق', NULL, NULL, NULL, 'active', '2025-07-21 14:06:04', '2025-07-21 14:06:04'),
(25, 'مقابيل ابو زهراء اقصاد', 'مقابيل ابو زهراء اقصاد', NULL, NULL, NULL, 'active', '2025-07-21 14:06:16', '2025-07-21 14:06:16'),
(26, 'رزاق لفته', 'رزاق لفته', NULL, NULL, NULL, 'active', '2025-07-21 14:06:28', '2025-07-21 14:06:28'),
(27, 'علي رزاق', 'علي رزاق', NULL, NULL, NULL, 'active', '2025-07-21 14:06:36', '2025-07-21 14:06:36'),
(28, 'بشارع الي يم المدرسه', 'بشارع الي يم المدرسه', NULL, NULL, NULL, 'active', '2025-07-21 14:07:00', '2025-07-21 14:07:00'),
(29, 'بالدربونه الي يم المدرسه', 'بالدربونه الي يم المدرسه', NULL, NULL, NULL, 'active', '2025-07-21 14:07:09', '2025-07-21 14:07:09'),
(31, 'علي التيل جوار بيت ابو حاتم', 'علي التيل جوار بيت ابو حاتم', NULL, NULL, NULL, 'active', '2025-07-21 14:07:45', '2025-07-21 14:07:45'),
(32, 'يم بيت مرزوقي', 'يم بيت مرزوقي', NULL, NULL, NULL, 'active', '2025-07-21 14:07:56', '2025-07-21 14:07:56'),
(33, 'يم مولدة سجاد سلام', 'يم مولدة سجاد سلام', NULL, NULL, NULL, 'active', '2025-07-21 14:08:03', '2025-07-21 14:08:03'),
(34, 'يم علي فاضل', 'يم علي فاضل', NULL, NULL, NULL, 'active', '2025-07-21 14:08:10', '2025-07-21 14:08:10'),
(35, 'ابراهيم انشائيه', 'ابراهيم انشائيه', NULL, NULL, NULL, 'active', '2025-07-21 14:08:19', '2025-07-21 14:08:19'),
(36, 'دربونه ادريس', 'دربونه ادريس', NULL, NULL, NULL, 'active', '2025-07-21 14:08:34', '2025-07-21 14:08:34'),
(37, 'يم مهند ابو الملابس', 'يم مهند ابو الملابس', NULL, NULL, NULL, 'active', '2025-07-21 14:08:42', '2025-07-21 14:08:42'),
(38, 'جوار صاله زيد ابوالاتاري', 'جوار صاله زيد ابوالاتاري', NULL, NULL, NULL, 'active', '2025-07-21 14:08:54', '2025-07-21 14:08:54'),
(39, 'يم مسلم ابو التنانير', 'يم مسلم ابو التنانير', NULL, NULL, NULL, 'active', '2025-07-21 14:09:01', '2025-07-21 14:09:01'),
(40, 'فارس الكريطي', 'فارس الكريطي', NULL, NULL, NULL, 'active', '2025-07-21 14:09:07', '2025-07-21 14:09:07'),
(41, 'تحسين الحلاق', 'تحسين الحلاق', NULL, NULL, NULL, 'active', '2025-07-21 14:09:15', '2025-07-21 14:09:15'),
(42, 'محمد الحمراني', 'محمد الحمراني', NULL, NULL, NULL, 'active', '2025-07-21 14:09:25', '2025-07-21 14:09:25'),
(43, 'سيد علي الحلاق', 'سيد علي الحلاق', NULL, NULL, NULL, 'active', '2025-07-21 14:09:33', '2025-07-21 14:09:33'),
(44, 'فوق حيدر اتاري بسطح', 'فوق حيدر اتاري بسطح', NULL, NULL, NULL, 'active', '2025-07-21 14:09:41', '2025-07-21 14:09:41'),
(45, 'رضا مطرود', 'رضا مطرود', NULL, NULL, NULL, 'active', '2025-07-21 14:09:51', '2025-07-21 14:09:51'),
(46, 'سيد غسان', 'سيد غسان', NULL, NULL, NULL, 'active', '2025-07-21 14:09:59', '2025-07-21 14:09:59'),
(47, 'طالب رحيم', 'طالب رحيم', NULL, NULL, NULL, 'active', '2025-07-21 14:10:06', '2025-07-21 14:10:06'),
(48, 'كابينة الشقة', 'كابينة الشقة', NULL, NULL, NULL, 'active', '2025-07-21 14:10:12', '2025-07-21 14:10:12'),
(49, 'كابينه دكتور ماجد', 'كابينه دكتور ماجد', NULL, NULL, NULL, 'active', '2025-07-21 14:10:20', '2025-07-21 14:10:20'),
(50, 'كابينه مولدة ابن عناد', 'كابينه مولدة ابن عناد', NULL, NULL, NULL, 'active', '2025-07-21 14:10:27', '2025-07-21 14:10:27'),
(51, 'مصطفى جوار فرقان', 'مصطفى جوار فرقان', NULL, NULL, NULL, 'active', '2025-07-21 14:10:37', '2025-07-21 14:10:37'),
(52, 'سجاد حرب', 'سجاد حرب', NULL, NULL, NULL, 'active', '2025-07-21 14:10:57', '2025-07-21 14:10:57'),
(53, 'بيت سيد ثامر', 'بيت سيد ثامر', NULL, NULL, NULL, 'active', '2025-07-21 14:11:06', '2025-07-21 14:11:06'),
(54, 'كابينه مقابيل بيت باله على البيت', 'كابينه مقابيل بيت باله على البيت', NULL, NULL, NULL, 'active', '2025-07-21 14:11:20', '2025-07-21 14:22:52'),
(55, 'ازياء سيد حمودي ', 'ازياء سيد حمودي ', NULL, NULL, NULL, 'active', '2025-07-21 14:11:26', '2025-07-21 14:22:45'),
(56, 'حيدر ابو الحجامه', 'حيدر ابو الحجامه', NULL, NULL, NULL, 'active', '2025-07-21 14:11:34', '2025-07-21 14:11:34'),
(57, 'فوك دائرة الكهرباء', 'فوك دائرة الكهرباء', NULL, NULL, NULL, 'active', '2025-07-21 14:11:42', '2025-07-21 14:11:42'),
(58, 'FAT1', 'FAT1', NULL, NULL, NULL, 'active', '2025-07-21 14:12:07', '2025-07-21 14:12:07'),
(59, 'FAT2', 'FAT2', NULL, NULL, NULL, 'active', '2025-07-21 14:12:19', '2025-07-21 14:12:19'),
(60, 'FAT3', 'FAT3', NULL, NULL, NULL, 'active', '2025-07-21 14:12:32', '2025-07-21 14:12:32'),
(61, 'FAT4', 'FAT4', NULL, NULL, NULL, 'active', '2025-07-21 14:12:39', '2025-07-21 14:12:39'),
(62, 'FAT5', 'FAT5', NULL, NULL, NULL, 'active', '2025-07-21 14:12:47', '2025-07-21 14:12:47'),
(63, 'FAT6', 'FAT6', NULL, NULL, NULL, 'active', '2025-07-21 14:12:53', '2025-07-21 14:12:53'),
(64, 'FAT7', 'FAT7', NULL, NULL, NULL, 'active', '2025-07-21 14:12:58', '2025-07-21 14:12:58'),
(65, 'FAT8', 'FAT8', NULL, NULL, NULL, 'active', '2025-07-21 14:13:03', '2025-07-21 14:13:03'),
(66, 'FAT9', 'FAT9', NULL, NULL, NULL, 'active', '2025-07-21 14:13:09', '2025-07-21 14:13:09'),
(67, 'FAT10', 'FAT10', NULL, NULL, NULL, 'active', '2025-07-21 14:13:15', '2025-07-21 14:13:15');

-- --------------------------------------------------------

--
-- Table structure for table `custom_field_options`
--

CREATE TABLE `custom_field_options` (
  `id` int NOT NULL,
  `field_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `option_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `option_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `option_label` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `color` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sort_order` int DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `created_by` int NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `device_types`
--

CREATE TABLE `device_types` (
  `id` int NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `discounts`
--

CREATE TABLE `discounts` (
  `id` int NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `discounts`
--

INSERT INTO `discounts` (`id`, `name`, `description`, `status`, `created_at`, `updated_at`) VALUES
(6, 'VIP', 'VIP', 'active', '2025-07-21 15:00:42', '2025-07-21 15:00:42'),
(7, 'ذهبي', 'ذهبي', 'active', '2025-07-21 15:00:50', '2025-07-21 15:00:50'),
(8, 'فضي', 'فضي', 'active', '2025-07-21 15:00:56', '2025-07-21 15:00:56'),
(9, 'مجاني', 'مجاني', 'active', '2025-07-21 15:01:02', '2025-07-21 15:01:02'),
(10, 'خاص', 'خاص', 'active', '2025-07-21 15:01:08', '2025-07-21 15:01:08'),
(11, 'فواتير', 'فواتير', 'active', '2025-07-21 15:01:13', '2025-07-21 15:01:13');

-- --------------------------------------------------------

--
-- Table structure for table `permissions`
--

CREATE TABLE `permissions` (
  `id` int NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `display_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `permissions`
--

INSERT INTO `permissions` (`id`, `name`, `display_name`, `category`, `description`, `created_at`) VALUES
(1, 'tickets.view_all', 'عرض جميع التذاكر', 'tickets', 'عرض جميع التذاكر في النظام', '2025-06-28 11:57:33'),
(2, 'tickets.view_own', 'عرض التذاكر الخاصة', 'tickets', 'عرض التذاكر المخصصة للمستخدم فقط', '2025-06-28 11:57:33'),
(3, 'tickets.create', 'إنشاء تذاكر', 'tickets', 'إنشاء تذاكر جديدة', '2025-06-28 11:57:33'),
(4, 'tickets.edit_all', 'تعديل جميع التذاكر', 'tickets', 'تعديل أي تذكرة في النظام', '2025-06-28 11:57:33'),
(5, 'tickets.edit_own', 'تعديل التذاكر الخاصة', 'tickets', 'تعديل التذاكر المخصصة للمستخدم فقط', '2025-06-28 11:57:33'),
(6, 'tickets.delete', 'حذف التذاكر', 'tickets', 'حذف التذاكر من النظام', '2025-06-28 11:57:33'),
(7, 'tickets.assign', 'تخصيص التذاكر', 'tickets', 'تخصيص التذاكر للوكلاء والموظفين', '2025-06-28 11:57:33'),
(8, 'tickets.change_status', 'تغيير حالة التذاكر', 'tickets', 'تغيير حالة التذاكر (مفتوحة، مغلقة، إلخ)', '2025-06-28 11:57:33'),
(9, 'tickets.add_comments', 'إضافة تعليقات', 'tickets', 'إضافة تعليقات على التذاكر', '2025-06-28 11:57:33'),
(10, 'tickets.attach_files', 'إرفاق ملفات', 'tickets', 'إرفاق ملفات مع التذاكر', '2025-06-28 11:57:33'),
(11, 'users.view', 'عرض المستخدمين', 'users', 'عرض قائمة المستخدمين', '2025-06-28 11:57:33'),
(12, 'users.create', 'إنشاء مستخدمين', 'users', 'إضافة مستخدمين جدد', '2025-06-28 11:57:33'),
(13, 'users.edit', 'تعديل المستخدمين', 'users', 'تعديل بيانات المستخدمين', '2025-06-28 11:57:33'),
(14, 'users.delete', 'حذف المستخدمين', 'users', 'حذف المستخدمين من النظام', '2025-06-28 11:57:33'),
(15, 'users.manage_permissions', 'إدارة صلاحيات المستخدمين', 'users', 'تعديل صلاحيات المستخدمين', '2025-06-28 11:57:33'),
(16, 'agents.view', 'عرض الوكلاء', 'agents', 'عرض قائمة الوكلاء', '2025-06-28 11:57:33'),
(17, 'agents.create', 'إنشاء وكلاء', 'agents', 'إضافة وكلاء جدد', '2025-06-28 11:57:33'),
(18, 'agents.edit', 'تعديل الوكلاء', 'agents', 'تعديل بيانات الوكلاء', '2025-06-28 11:57:33'),
(19, 'agents.delete', 'حذف الوكلاء', 'agents', 'حذف الوكلاء من النظام', '2025-06-28 11:57:33'),
(20, 'teams.view', 'عرض الفرق', 'teams', 'عرض قائمة الفرق', '2025-06-28 11:57:33'),
(21, 'teams.create', 'إنشاء فرق', 'teams', 'إنشاء فرق جديدة', '2025-06-28 11:57:33'),
(22, 'teams.edit', 'تعديل الفرق', 'teams', 'تعديل بيانات الفرق', '2025-06-28 11:57:33'),
(23, 'teams.delete', 'حذف الفرق', 'teams', 'حذف الفرق من النظام', '2025-06-28 11:57:33'),
(24, 'teams.manage_members', 'إدارة أعضاء الفرق', 'teams', 'إضافة وإزالة أعضاء الفرق', '2025-06-28 11:57:33'),
(25, 'reports.view', 'عرض التقارير', 'reports', 'عرض التقارير والإحصائيات', '2025-06-28 11:57:33'),
(26, 'reports.export', 'تصدير التقارير', 'reports', 'تصدير التقارير بصيغ مختلفة', '2025-06-28 11:57:33'),
(27, 'notifications.view', 'عرض الإشعارات', 'notifications', 'عرض الإشعارات الخاصة', '2025-06-28 11:57:33'),
(28, 'notifications.manage', 'إدارة الإشعارات', 'notifications', 'إدارة إعدادات الإشعارات', '2025-06-28 11:57:33'),
(29, 'system.settings', 'إعدادات النظام', 'system', 'الوصول لإعدادات النظام العامة', '2025-06-28 11:57:33'),
(30, 'system.backup', 'النسخ الاحتياطي', 'system', 'إنشاء واستعادة النسخ الاحتياطية', '2025-06-28 11:57:33'),
(31, 'system.logs', 'سجلات النظام', 'system', 'عرض سجلات النظام والأنشطة', '2025-06-28 11:57:33'),
(32, 'roles.view', 'عرض الأدوار', 'roles', 'عرض قائمة الأدوار', '2025-06-28 11:57:33'),
(33, 'roles.create', 'إنشاء أدوار', 'roles', 'إنشاء أدوار مخصصة جديدة', '2025-06-28 11:57:33'),
(34, 'roles.edit', 'تعديل الأدوار', 'roles', 'تعديل الأدوار وصلاحياتها', '2025-06-28 11:57:33'),
(35, 'roles.delete', 'حذف الأدوار', 'roles', 'حذف الأدوار المخصصة', '2025-06-28 11:57:33'),
(106, 'agents.view_all', 'عرض جميع الوكلاء', 'agents', 'يمكن عرض قائمة جميع الوكلاء', '2025-06-28 12:59:09'),
(107, 'tickets.edit', 'تعديل التذاكر', 'tickets', 'يمكن تعديل التذاكر', '2025-06-28 12:59:09'),
(108, 'users.manage', 'إدارة المستخدمين', 'users', 'يمكن إدارة حسابات المستخدمين', '2025-06-28 12:59:09'),
(109, 'system.admin_access', 'الوصول لإعدادات النظام', 'system', 'يمكن الوصول لإعدادات النظام', '2025-06-28 12:59:09'),
(110, 'system.permissions', 'إدارة الصلاحيات', 'system', 'يمكن إدارة صلاحيات المستخدمين', '2025-06-28 12:59:09'),
(111, 'teams.manage', 'إدارة الفرق', 'teams', 'يمكن إدارة الفرق والأعضاء', '2025-06-28 12:59:09'),
(112, 'teams.assign', 'تخصيص الفرق', 'teams', 'يمكن تخصيص المهام للفرق', '2025-06-28 12:59:09'),
(122, 'permissions.manage', 'إدارة الصلاحيات', 'permissions', 'يمكن منح وإلغاء الصلاحيات', '2025-06-28 13:36:14'),
(124, 'backup.create', 'إنشاء نسخ احتياطية', 'system', 'يمكن إنشاء نسخ احتياطية', '2025-06-28 13:36:14');

-- --------------------------------------------------------

--
-- Table structure for table `ports`
--

CREATE TABLE `ports` (
  `id` int NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `ports`
--

INSERT INTO `ports` (`id`, `name`, `description`, `status`, `created_at`, `updated_at`) VALUES
(6, 'P1', 'P1', 'active', '2025-07-21 14:14:02', '2025-07-21 14:14:02'),
(7, 'P2', 'P2', 'active', '2025-07-21 14:14:11', '2025-07-21 14:14:11'),
(8, 'P3', 'P3', 'active', '2025-07-21 14:14:17', '2025-07-21 14:14:17'),
(9, 'P4', 'P4', 'active', '2025-07-21 14:14:24', '2025-07-21 14:14:24'),
(10, 'P5', 'P5', 'active', '2025-07-21 14:14:31', '2025-07-21 14:14:31'),
(11, 'P6', 'P6', 'active', '2025-07-21 14:14:37', '2025-07-21 14:14:37'),
(12, 'P7', 'P7', 'active', '2025-07-21 14:14:43', '2025-07-21 14:14:43'),
(13, 'P8', 'P8', 'active', '2025-07-21 14:14:50', '2025-07-21 14:14:50'),
(14, 'P9', 'P9', 'active', '2025-07-21 14:14:57', '2025-07-21 14:14:57'),
(15, 'P10', 'P10', 'active', '2025-07-21 14:15:02', '2025-07-21 14:15:02'),
(16, 'P11', 'P11', 'active', '2025-07-21 14:15:08', '2025-07-21 14:15:08'),
(17, 'P12', 'P12', 'active', '2025-07-21 14:15:15', '2025-07-21 14:15:15'),
(18, 'P13', 'P13', 'active', '2025-07-21 14:15:23', '2025-07-21 14:15:23'),
(19, 'P14', 'P14', 'active', '2025-07-21 14:15:29', '2025-07-21 14:15:29'),
(20, 'P15', 'P15', 'active', '2025-07-21 14:15:34', '2025-07-21 14:15:34'),
(21, 'P16', 'P16', 'active', '2025-07-21 14:15:40', '2025-07-21 14:15:40');

-- --------------------------------------------------------

--
-- Table structure for table `repeaters`
--

CREATE TABLE `repeaters` (
  `id` int NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `status` enum('active','inactive') COLLATE utf8mb4_unicode_ci DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `latitude` decimal(10,8) DEFAULT NULL,
  `longitude` decimal(11,8) DEFAULT NULL,
  `coverage_radius` int DEFAULT NULL COMMENT 'نطاق التغطية بالأمتار',
  `antenna_height` int DEFAULT NULL COMMENT 'ارتفاع الهوائي بالأمتار',
  `tower_id` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `repeaters`
--

INSERT INTO `repeaters` (`id`, `name`, `description`, `status`, `created_at`, `latitude`, `longitude`, `coverage_radius`, `antenna_height`, `tower_id`) VALUES
(5, 'البراكية', 'البراكية', 'active', '2024-12-03 12:54:52', 32.00072237, 44.41554099, 2500, 0, NULL),
(6, 'النداء', 'النداء', 'active', '2024-12-03 13:04:14', 32.08569961, 44.31608266, 2500, 0, NULL),
(7, 'العباسية', 'العباسية', 'active', '2024-12-03 16:11:23', 32.07938820, 44.44757756, 2500, 0, NULL),
(8, 'الكوفة', 'الكوفة', 'active', '2024-12-03 16:11:34', 32.03123266, 44.40552248, 2500, 0, NULL),
(10, 'رعاش', 'رعاش', 'active', '2024-12-03 16:11:50', 0.00000000, 0.00000000, 0, 0, NULL),
(11, 'ميسان', 'ميسان', 'active', '2024-12-03 16:12:07', 32.05732726, 44.36549940, 2500, 0, NULL),
(12, 'الملعب', 'الملعب', 'active', '2024-12-03 16:12:34', 32.01315203, 44.32809611, 2500, 0, NULL),
(13, 'النصر', 'النصر', 'active', '2024-12-03 16:12:45', 32.04655834, 44.31547305, 2500, 0, NULL),
(14, 'الجامعة', 'الجامعة', 'active', '2024-12-03 16:13:11', 32.03056300, 44.34869700, 2500, 0, NULL),
(16, 'الميلاد', 'الميلاد', 'active', '2024-12-03 16:13:30', 32.06067242, 44.31494818, 2500, 0, NULL),
(17, 'الوفاء-مهند', 'الوفاء-مهند', 'active', '2024-12-03 16:13:37', 32.05675466, 44.33626143, 2500, 0, NULL),
(18, 'مكتب نجف توب', 'مكتب نجف توب', 'active', '2024-12-03 16:13:45', 32.00581429, 44.34426592, 2500, 0, NULL),
(19, 'ربيتر-احمد الخيكاني(شارع المطار جديد)', 'ربيتر-احمد الخيكاني(شارع المطار جديد)', 'active', '2024-12-03 16:13:53', 0.00000000, 0.00000000, 0, 0, NULL),
(22, 'مكتب نجف اونلاين', 'مكتب نجف اونلاين', 'active', '2024-12-03 16:14:15', 32.01040486, 44.34453429, 2500, 0, NULL),
(33, 'ربيتر حي الانصار', 'ربيتر حي الانصار', 'active', '2024-12-03 16:25:53', 31.98548946, 44.35783318, 2500, 0, NULL),
(34, 'المطار', 'المطار', 'active', '2024-12-07 14:14:36', 32.00665300, 44.40009400, 2500, NULL, NULL),
(36, 'الولاية', 'الولاية', 'active', '2024-12-09 16:33:18', 31.99656188, 44.32048555, 2500, NULL, NULL),
(37, 'ربيتر-مهند شمران مكتب الرشيد', 'ربيتر-مهند شمران', 'active', '2024-12-10 08:36:52', 32.05320647, 44.32758336, 2500, NULL, NULL),
(38, 'ربيتر-سيف احسان', 'ربيتر-سيف احسان', 'active', '2024-12-10 11:51:13', 32.05515332, 44.36089601, 0, NULL, NULL),
(39, 'المناذرة', 'المناذرة', 'active', '2024-12-15 07:55:48', 0.00000000, 0.00000000, 2500, NULL, NULL),
(41, 'ربيتر امير حلاوة', 'ربيتر امير حلاوة', 'active', '2024-12-24 16:46:29', 32.03982079, 44.34357706, 2500, NULL, NULL),
(42, 'ربيتر الاسكان', 'ربيتر الاسكان', 'active', '2025-01-26 11:24:23', 32.00212367, 44.35033478, 2500, NULL, NULL),
(43, 'ربيتر الوفاء', 'ربيتر الوفاء الشركه كوك نت', 'active', '2025-02-17 12:29:16', 32.05646034, 44.33626232, 2500, NULL, NULL),
(44, 'ربيتر حي الزهراء تابع الشركه', 'ربيتر حي الزهراء تابع الشركه', 'active', '2025-03-19 17:38:14', 31.99930100, 44.36448800, 2500, NULL, NULL),
(45, 'شارع كربلاء عمود79', 'شارع كربلاء عمود79', 'active', '2025-03-20 20:10:29', 32.09659500, 44.32327600, 2500, NULL, NULL),
(47, 'ربيتر حسين كروان', 'حسين كروان', 'active', '2025-04-09 12:07:30', 32.02204200, 44.41044700, 2500, NULL, NULL),
(48, 'ربيترات', 'ربيتر الربيترات', 'active', '2025-04-16 12:20:38', 45.89319400, 23.54761100, 1000, NULL, NULL),
(49, 'سب ربيتر احمد البعيجي', '', 'active', '2025-05-08 16:06:21', 32.02077700, 44.40658400, 1000, NULL, NULL),
(50, 'سب ربيتر سيد منتظر-المناذرة', 'سب ربيتر سيد منتظر-المناذرة', 'active', '2025-05-08 17:54:49', 31.99166211, 44.31256306, 2500, NULL, NULL),
(51, 'ربيتر-النداء-تابع الشركة', 'ربيتر-النداء-تابع الشركة', 'active', '2025-05-28 14:01:57', 32.01006900, 44.42061500, 2500, NULL, NULL),
(52, 'ربيتر الانصار-تابع الشركة', 'ربيتر الانصار-تابع الشركة', 'active', '2025-07-02 18:55:44', 32.01006900, 44.42061500, 2500, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `service_types`
--

CREATE TABLE `service_types` (
  `id` int NOT NULL,
  `name` varchar(255) NOT NULL,
  `code` varchar(50) NOT NULL,
  `description` text,
  `price` decimal(10,2) DEFAULT NULL,
  `color` varchar(7) DEFAULT '#007bff',
  `icon` varchar(50) DEFAULT 'bi-wifi',
  `status` enum('active','inactive') DEFAULT 'active',
  `sort_order` int DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `service_types`
--

INSERT INTO `service_types` (`id`, `name`, `code`, `description`, `price`, `color`, `icon`, `status`, `sort_order`, `created_at`, `updated_at`) VALUES
(13, 'ايرث لنك', '_13', 'ايرث لنك', NULL, '#007bff', 'bi-wifi', 'active', 0, '2025-07-21 14:29:41', '2025-07-21 14:29:46'),
(14, 'NajafOnline25', 'NAJAFONLIN_1753108194', 'NajafOnline25', NULL, '#007bff', 'bi-wifi', 'active', 0, '2025-07-21 14:29:54', '2025-07-21 14:29:54'),
(15, 'Topnajaf1', 'TOPNAJAF1_1753108203', 'Topnajaf1', NULL, '#007bff', 'bi-wifi', 'active', 0, '2025-07-21 14:30:03', '2025-07-21 14:30:03'),
(16, 'NajafOnline126', 'NAJAFONLIN_1753108209', 'NajafOnline126', NULL, '#007bff', 'bi-wifi', 'active', 0, '2025-07-21 14:30:09', '2025-07-21 14:30:09'),
(17, 'سبيد لنك برود باند', '_1753108219', 'سبيد لنك برود باند', NULL, '#007bff', 'bi-wifi', 'active', 0, '2025-07-21 14:30:19', '2025-07-21 14:30:19'),
(18, 'NajafOnline19', 'NAJAFONLIN_1753108226', 'NajafOnline19', NULL, '#007bff', 'bi-wifi', 'active', 0, '2025-07-21 14:30:26', '2025-07-21 14:30:26'),
(19, 'NajafOnline12', 'NAJAFONLIN_1753108233', 'NajafOnline12', NULL, '#007bff', 'bi-wifi', 'active', 0, '2025-07-21 14:30:33', '2025-07-21 14:30:33'),
(20, 'اول تيليكوم', '_1753108246', 'اول تيليكوم', NULL, '#007bff', 'bi-wifi', 'active', 0, '2025-07-21 14:30:46', '2025-07-21 14:30:46'),
(21, 'TL-n20', 'TLN20_1753108257', 'TL-n20', NULL, '#007bff', 'bi-wifi', 'active', 0, '2025-07-21 14:30:57', '2025-07-21 14:30:57'),
(22, 'TL-n28', 'TLN28_1753108268', 'TL-n28', NULL, '#007bff', 'bi-wifi', 'active', 0, '2025-07-21 14:31:08', '2025-07-21 14:31:08'),
(23, 'FTTH-174', 'FTTH174_1753108276', 'FTTH-174', NULL, '#007bff', 'bi-wifi', 'active', 0, '2025-07-21 14:31:16', '2025-07-21 14:31:16'),
(24, 'speedlink', 'SPEEDLINK_1753108284', 'speedlink', NULL, '#007bff', 'bi-wifi', 'active', 0, '2025-07-21 14:31:24', '2025-07-21 14:31:24'),
(25, 'NajafOnline', 'NAJAFONLIN_1753108298', 'NajafOnline', NULL, '#007bff', 'bi-wifi', 'active', 0, '2025-07-21 14:31:38', '2025-07-21 14:31:38'),
(26, 'NajafOnline30', 'NAJAFONLIN_1753108304', 'NajafOnline30', NULL, '#007bff', 'bi-wifi', 'active', 0, '2025-07-21 14:31:44', '2025-07-21 14:31:44'),
(27, 'tlnn1', 'TLNN1_1753108310', 'tlnn1', NULL, '#007bff', 'bi-wifi', 'active', 0, '2025-07-21 14:31:50', '2025-07-21 14:31:50'),
(28, 'tln5', 'TLN5_1753108316', 'tln5', NULL, '#007bff', 'bi-wifi', 'active', 0, '2025-07-21 14:31:56', '2025-07-21 14:31:56'),
(29, 'NajafOnline3', 'NAJAFONLIN_1753108325', 'NajafOnline3', NULL, '#007bff', 'bi-wifi', 'active', 0, '2025-07-21 14:32:05', '2025-07-21 14:32:05'),
(30, 'NajafOnline4', 'NAJAFONLIN_1753108333', 'NajafOnline4', NULL, '#007bff', 'bi-wifi', 'active', 0, '2025-07-21 14:32:13', '2025-07-21 14:32:13'),
(31, 'NajafOnline10', 'NAJAFONLIN_1753108340', 'NajafOnline10', NULL, '#007bff', 'bi-wifi', 'active', 0, '2025-07-21 14:32:20', '2025-07-21 14:32:20'),
(32, 'الوطني', '_1753108346', 'الوطني', NULL, '#007bff', 'bi-wifi', 'active', 0, '2025-07-21 14:32:26', '2025-07-21 14:32:26'),
(33, 'NajafOnline26', 'NAJAFONLIN_1753108352', 'NajafOnline26', NULL, '#007bff', 'bi-wifi', 'active', 0, '2025-07-21 14:32:32', '2025-07-21 14:32:32'),
(34, 'NajafOnline6', 'NAJAFONLIN_1753108358', 'NajafOnline6', NULL, '#007bff', 'bi-wifi', 'active', 0, '2025-07-21 14:32:38', '2025-07-21 14:32:38'),
(35, 'tln6', 'TLN6_1753108364', 'tln6', NULL, '#007bff', 'bi-wifi', 'active', 0, '2025-07-21 14:32:44', '2025-07-21 14:32:44'),
(36, 'tln9', 'TLN9_1753108370', 'tln9', NULL, '#007bff', 'bi-wifi', 'active', 0, '2025-07-21 14:32:50', '2025-07-21 14:32:50'),
(37, 'NajafOnline64', 'NAJAFONLIN_1753108376', 'NajafOnline64', NULL, '#007bff', 'bi-wifi', 'active', 0, '2025-07-21 14:32:56', '2025-07-21 14:32:56'),
(38, 'TOP5', 'TOP5_1753108382', 'TOP5', NULL, '#007bff', 'bi-wifi', 'active', 0, '2025-07-21 14:33:02', '2025-07-21 14:33:02'),
(39, 'tln10', 'TLN10_1753108389', 'tln10', NULL, '#007bff', 'bi-wifi', 'active', 0, '2025-07-21 14:33:09', '2025-07-21 14:33:09');

-- --------------------------------------------------------

--
-- Table structure for table `setting_categories`
--

CREATE TABLE `setting_categories` (
  `id` int NOT NULL,
  `category_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `category_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'gear',
  `color` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'primary',
  `sort_order` int DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `setting_categories`
--

INSERT INTO `setting_categories` (`id`, `category_key`, `category_name`, `description`, `icon`, `color`, `sort_order`, `is_active`, `created_at`) VALUES
(1, 'general', 'الإعدادات العامة', 'إعدادات عامة للنظام', 'gear', 'primary', 1, 1, '2025-06-28 11:07:55'),
(2, 'tickets', 'إعدادات التذاكر', 'إعدادات نظام التذاكر والفئات والأولويات', 'ticket', 'info', 2, 1, '2025-06-28 11:07:55'),
(3, 'notifications', 'إعدادات الإشعارات', 'إعدادات الإشعارات والتنبيهات', 'bell', 'warning', 3, 1, '2025-06-28 11:07:55'),
(4, 'monitoring', 'إعدادات المراقبة', 'إعدادات مراقبة الوكلاء والشبكة', 'activity', 'success', 4, 1, '2025-06-28 11:07:55'),
(5, 'telegram', 'إعدادات تيليجرام', 'إعدادات بوت تيليجرام والإشعارات', 'telegram', 'info', 5, 1, '2025-06-28 11:07:55'),
(6, 'backup', 'إعدادات النسخ الاحتياطي', 'إعدادات النسخ الاحتياطي والاستعادة', 'cloud-arrow-up', 'secondary', 6, 1, '2025-06-28 11:07:55'),
(7, 'security', 'إعدادات الأمان', 'إعدادات الأمان وكلمات المرور', 'shield-check', 'danger', 7, 1, '2025-06-28 11:07:55'),
(8, 'teams', 'إعدادات الفرق', 'إعدادات الفرق وتدفق العمل', 'people', 'purple', 8, 1, '2025-06-28 11:07:55');

-- --------------------------------------------------------

--
-- Table structure for table `setting_change_log`
--

CREATE TABLE `setting_change_log` (
  `id` int NOT NULL,
  `setting_id` int NOT NULL,
  `old_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `new_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `changed_by` int NOT NULL,
  `change_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `changed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `simple_agents`
--

CREATE TABLE `simple_agents` (
  `id` int NOT NULL,
  `agent_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone_number` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `system_settings`
--

CREATE TABLE `system_settings` (
  `id` int NOT NULL,
  `category_id` int NOT NULL,
  `setting_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `setting_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `setting_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `default_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `setting_type` enum('string','number','boolean','json','enum','text','email','url','password','color','date','time','datetime') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'string',
  `validation_rules` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'قواعد التحقق بصيغة JSON',
  `enum_options` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'خيارات القائمة المنسدلة بصيغة JSON',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `help_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `is_required` tinyint(1) NOT NULL DEFAULT '0',
  `is_readonly` tinyint(1) NOT NULL DEFAULT '0',
  `is_sensitive` tinyint(1) NOT NULL DEFAULT '0',
  `sort_order` int DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `towers`
--

CREATE TABLE `towers` (
  `id` int NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `latitude` decimal(10,8) NOT NULL,
  `longitude` decimal(11,8) NOT NULL,
  `height` decimal(10,2) DEFAULT NULL,
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `tower_locations`
--

CREATE TABLE `tower_locations` (
  `id` int NOT NULL,
  `name` varchar(255) NOT NULL,
  `code` varchar(50) NOT NULL,
  `description` text,
  `address` varchar(500) DEFAULT NULL,
  `latitude` decimal(10,8) DEFAULT NULL,
  `longitude` decimal(11,8) DEFAULT NULL,
  `height` decimal(8,2) DEFAULT NULL,
  `coverage_radius` int DEFAULT NULL,
  `capacity` int DEFAULT NULL,
  `status` enum('active','inactive','maintenance') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `tower_locations`
--

INSERT INTO `tower_locations` (`id`, `name`, `code`, `description`, `address`, `latitude`, `longitude`, `height`, `coverage_radius`, `capacity`, `status`, `created_at`, `updated_at`) VALUES
(6, 'سبيد لنك الكوفة', '_1753108442', 'سبيد لنك الكوفة', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:34:02', '2025-07-21 14:34:02'),
(7, 'مكتب x', 'X_1753108451', 'مكتب x', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:34:11', '2025-07-21 14:34:11'),
(8, 'عقيل الفتلاوي', '_1753108461', 'عقيل الفتلاوي', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:34:21', '2025-07-21 14:34:21'),
(9, 'فاضل الشعبي', '_1753108469', 'فاضل الشعبي', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:34:29', '2025-07-21 14:34:29'),
(10, 'كرار سعد', '_1753108659', 'كرار سعد', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:37:39', '2025-07-21 14:37:39'),
(11, 'مصطفى حسن', '_1753108694', 'مصطفى حسن', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:38:14', '2025-07-21 14:38:14'),
(12, 'مصطفى خضير', '_1753108703', 'مصطفى خضير', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:38:23', '2025-07-21 14:38:23'),
(13, 'عقيل رزاق', '_1753108710', 'عقيل رزاق', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:38:30', '2025-07-21 14:38:30'),
(14, 'سيف احسان', '_1753108718', 'سيف احسان', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:38:38', '2025-07-21 14:38:38'),
(15, 'برج الجمهورية', '_1753108729', 'برج الجمهورية', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:38:49', '2025-07-21 14:38:49'),
(16, 'برج فيض الكوفة', '_1753108740', 'برج فيض الكوفة', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:39:00', '2025-07-21 14:39:00'),
(17, 'برج العلوة', '_1753108747', 'برج العلوة', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:39:07', '2025-07-21 14:39:07'),
(18, 'عبد الحسين الخباز', '_1753108754', 'عبد الحسين الخباز', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:39:14', '2025-07-21 14:39:14'),
(19, 'برج زيد ميسان', '_1753108761', 'برج زيد ميسان', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:39:21', '2025-07-21 14:39:21'),
(20, 'برج السكة', '_1753108768', 'برج السكة', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:39:28', '2025-07-21 14:39:28'),
(21, 'البرج الرئيسي-ضوئي', '_1753108782', 'البرج الرئيسي-ضوئي', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:39:42', '2025-07-21 14:39:42'),
(22, 'برج ياسين', '_1753108791', 'برج ياسين', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:39:51', '2025-07-21 14:39:51'),
(23, 'برج فندق ام القرى', '_1753108799', 'برج فندق ام القرى', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:39:59', '2025-07-21 14:39:59'),
(24, 'هشام الفتلاوي البيت', '_1753108826', 'هشام الفتلاوي البيت', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:40:26', '2025-07-21 14:40:26'),
(25, 'مكتب البراكية', '_1753108835', 'مكتب البراكية', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:40:35', '2025-07-21 14:40:35'),
(26, 'فندق وردة النجف', '_1753108843', 'فندق وردة النجف', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:40:43', '2025-07-21 14:40:43'),
(27, 'الاسكان واير لنك', '_1753108851', 'الاسكان واير لنك', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:40:51', '2025-07-21 14:40:51'),
(28, 'جامعة الصادق', '_1753108859', 'جامعة الصادق', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:40:59', '2025-07-21 14:40:59'),
(29, 'الملعب', '_1753108867', 'الملعب', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:41:07', '2025-07-21 14:41:07'),
(30, 'مجمع المختار', '_1753108874', 'مجمع المختار', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:41:14', '2025-07-21 14:41:14'),
(31, 'ربيتر الميلاد', '_1753108881', 'ربيتر الميلاد', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:41:21', '2025-07-21 14:41:21'),
(32, 'فندق قصر المولى', '_1753108887', 'فندق قصر المولى', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:41:27', '2025-07-21 14:41:27'),
(33, 'الجنسية علي سمارت', '_1753108896', 'الجنسية علي سمارت', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:41:36', '2025-07-21 14:41:36'),
(34, 'فندق جنة علي', '_1753108903', 'فندق جنة علي', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:41:43', '2025-07-21 14:41:43'),
(35, 'فندق العطاء', '_1753108911', 'فندق العطاء', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:41:51', '2025-07-21 14:41:51'),
(36, 'البراكية2-BR814', '2BR814_1753108919', 'البراكية2-BR814', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:41:59', '2025-07-21 14:41:59'),
(37, 'اسواق الكرادة الشرقية', '_1753108926', 'اسواق الكرادة الشرقية', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:42:06', '2025-07-21 14:42:06'),
(38, 'مكتب سيد علاء', '_1753108940', 'مكتب سيد علاء', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:42:20', '2025-07-21 14:42:20'),
(39, 'مكتب نجف اونلاين', '_1753108947', 'مكتب نجف اونلاين', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:42:27', '2025-07-21 14:42:27'),
(40, 'سبيد ماكس', '_1753109077', 'سبيد ماكس', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:44:37', '2025-07-21 14:44:37'),
(41, 'مكتب الشرق-غازي', '_1753109085', 'مكتب الشرق-غازي', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:44:45', '2025-07-21 14:44:45'),
(42, 'اسواق زيد ابن علي', '_1753109094', 'اسواق زيد ابن علي', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:44:54', '2025-07-21 14:44:54'),
(43, 'ربيتر النداء', '_1753109102', 'ربيتر النداء', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:45:02', '2025-07-21 14:45:02'),
(44, 'كوفي ليالي الفرات', '_1753109113', 'كوفي ليالي الفرات', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:45:13', '2025-07-21 14:45:13'),
(45, 'برج منتظر قاسم', '_1753109121', 'برج منتظر قاسم', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:45:21', '2025-07-21 14:45:21'),
(46, 'الحسيسنة عمود79', '79_1753109128', 'الحسيسنة عمود79', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:45:28', '2025-07-21 14:45:28'),
(47, 'برج بيت زيد جواد', '_1753109180', 'برج بيت زيد جواد', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:46:20', '2025-07-21 14:46:20'),
(48, 'ربيتر الانصار', '_1753109193', 'ربيتر الانصار', NULL, NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-21 14:46:33', '2025-07-21 14:46:33');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int NOT NULL,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `full_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `role` enum('admin','employee') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'employee',
  `role_id` int DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `last_login` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `username`, `password`, `full_name`, `email`, `phone`, `role`, `role_id`, `is_active`, `last_login`, `created_at`, `updated_at`, `created_by`) VALUES
(1, 'admin', '$2y$10$h.LqEVxKJoWZboA4AldGruzUduFwaOMck506BfXCOI55kMGvRkcGC', 'najafonline', '<EMAIL>', '07855808080', 'admin', 2, 1, '2025-07-21 21:44:50', '2025-01-23 08:52:36', '2025-07-21 18:44:50', NULL),
(6, 'admin2', '$2y$10$2w3lavxKAhy.bf.uf0POaeJpsW.7hDP6VBX0gMgy/LYaKJxchX62G', 'safe Ihsan', '', '', 'admin', 2, 1, '2025-02-17 17:16:51', '2025-02-15 16:50:30', '2025-06-28 12:59:10', 1),
(8, ' ', '$2y$10$kNZM08q/DvQUO7kDXAzl2eh83Agk7J0o1fcDGY6hYJC3Au0XbWytO', 'safe Ihsan', '', '', 'admin', 2, 1, '2025-07-10 17:40:59', '2025-02-17 17:17:39', '2025-07-10 17:40:59', 6),
(10, 'speedlink', '$2y$10$Kc9ryWgi.ZrJ6WR0s37LSeyVA0iH1pTN/dn.86vrWzuWdsBxSN67.', 'speedlink', '', '', 'employee', 4, 1, '2025-06-10 07:09:25', '2025-06-09 09:48:48', '2025-06-28 12:59:10', 1),
(11, 'Hayder', '$2y$10$n91/gYHMegaknFgxozmejORuYDzynw5ZL75E1rP1UBaJR02KBpQsa', 'Hayder', '', '', 'admin', 2, 1, '2025-06-09 09:51:11', '2025-06-09 09:51:02', '2025-06-28 12:59:10', 1),
(14, 'ali', '$2y$10$JSrlHCTCvNpKmhUf2viJQe1DbeAGG6gPnf/yft/OdPr0/q6gSRM.O', 'علي حمزه محمد', '<EMAIL>', '07815659841', 'employee', 7, 1, '2025-06-30 18:00:34', '2025-06-28 15:04:21', '2025-06-30 18:00:34', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `user_notification_settings`
--

CREATE TABLE `user_notification_settings` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `notification_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1',
  `delivery_method` set('internal','email','telegram') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'internal',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_notification_settings`
--

INSERT INTO `user_notification_settings` (`id`, `user_id`, `notification_type`, `is_enabled`, `delivery_method`, `created_at`, `updated_at`) VALUES
(1, 1, 'agent_status_changed', 0, 'internal', '2025-06-26 18:33:05', '2025-06-28 11:52:30'),
(2, 1, 'user_login', 0, 'internal', '2025-06-26 18:33:05', '2025-06-28 11:52:30'),
(3, 1, 'backup_completed', 0, 'internal', '2025-06-26 18:33:05', '2025-06-28 11:52:30'),
(4, 1, 'system_error', 0, 'internal', '2025-06-26 18:33:05', '2025-06-26 18:33:05'),
(5, 1, 'system_maintenance', 0, 'internal', '2025-06-26 18:33:05', '2025-06-26 18:33:05'),
(6, 1, 'ticket_closed', 0, 'internal', '2025-06-26 18:33:05', '2025-06-28 11:52:30'),
(7, 1, 'ticket_created', 0, 'internal', '2025-06-26 18:33:05', '2025-06-28 11:52:30'),
(8, 1, 'ticket_comment_added', 0, 'internal', '2025-06-26 18:33:05', '2025-06-28 11:52:30'),
(9, 1, 'ticket_assigned', 0, 'internal', '2025-06-26 18:33:05', '2025-06-26 18:33:05'),
(10, 1, 'ticket_status_changed', 0, 'internal', '2025-06-26 18:33:05', '2025-06-28 11:52:30'),
(11, 1, 'ticket_resolved', 0, 'internal', '2025-06-26 18:33:06', '2025-06-28 11:52:30');

-- --------------------------------------------------------

--
-- Table structure for table `user_permissions`
--

CREATE TABLE `user_permissions` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `permission_id` int NOT NULL,
  `granted` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_permissions`
--

INSERT INTO `user_permissions` (`id`, `user_id`, `permission_id`, `granted`, `created_at`, `updated_at`) VALUES
(136, 14, 17, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(137, 14, 18, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(138, 14, 19, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(139, 14, 16, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(140, 14, 106, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(141, 14, 28, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(142, 14, 27, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(143, 14, 122, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(144, 14, 26, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(145, 14, 25, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(146, 14, 33, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(147, 14, 34, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(148, 14, 35, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(149, 14, 32, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(150, 14, 110, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(151, 14, 29, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(152, 14, 124, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(153, 14, 30, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(154, 14, 109, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(155, 14, 31, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(156, 14, 24, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(157, 14, 111, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(158, 14, 21, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(159, 14, 112, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(160, 14, 22, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(161, 14, 23, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(162, 14, 20, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(163, 14, 10, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(164, 14, 9, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(165, 14, 3, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(166, 14, 7, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(167, 14, 107, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(168, 14, 5, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(169, 14, 4, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(170, 14, 8, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(171, 14, 6, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(172, 14, 2, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(173, 14, 1, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(174, 14, 108, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(175, 14, 15, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(176, 14, 12, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(177, 14, 13, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(178, 14, 14, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23'),
(179, 14, 11, 1, '2025-06-28 15:06:23', '2025-06-28 15:06:23');

-- --------------------------------------------------------

--
-- Table structure for table `user_permissions_backup`
--

CREATE TABLE `user_permissions_backup` (
  `id` int NOT NULL DEFAULT '0',
  `user_id` int NOT NULL,
  `can_add_agents` tinyint(1) DEFAULT '0',
  `can_edit_agents` tinyint(1) DEFAULT '0',
  `can_delete_agents` tinyint(1) DEFAULT '0',
  `can_view_reports` tinyint(1) DEFAULT '0',
  `can_manage_users` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_permissions_backup`
--

INSERT INTO `user_permissions_backup` (`id`, `user_id`, `can_add_agents`, `can_edit_agents`, `can_delete_agents`, `can_view_reports`, `can_manage_users`) VALUES
(1, 1, 1, 1, 1, 1, 1),
(6, 6, 1, 1, 1, 1, 1),
(8, 8, 1, 1, 1, 1, 1),
(10, 10, 0, 0, 0, 1, 0),
(11, 11, 1, 1, 1, 1, 1);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `activity_log`
--
ALTER TABLE `activity_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `agents`
--
ALTER TABLE `agents`
  ADD PRIMARY KEY (`id`),
  ADD KEY `repeater_id` (`repeater_id`),
  ADD KEY `device_type_id` (`device_type_id`),
  ADD KEY `fk_agents_port` (`port_id`),
  ADD KEY `fk_agents_cabinet_location` (`cabinet_location_id`),
  ADD KEY `fk_agents_discount` (`discount_id`),
  ADD KEY `fk_agents_service_type` (`service_type_id`),
  ADD KEY `fk_agents_belongs_to` (`belongs_to_id`),
  ADD KEY `fk_agents_branch_name` (`branch_name_id`),
  ADD KEY `fk_agents_tower_location` (`tower_location_id`);

--
-- Indexes for table `agent_connection_logs`
--
ALTER TABLE `agent_connection_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `agent_id` (`agent_id`);

--
-- Indexes for table `agent_ips`
--
ALTER TABLE `agent_ips`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `idx_agent_ips_unique` (`agent_id`,`ip_address`),
  ADD KEY `idx_agent_ips_agent_id` (`agent_id`);

--
-- Indexes for table `agent_snmp_settings`
--
ALTER TABLE `agent_snmp_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_agent` (`agent_id`);

--
-- Indexes for table `agent_status_logs`
--
ALTER TABLE `agent_status_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `agent_id` (`agent_id`);

--
-- Indexes for table `agent_uptime_stats`
--
ALTER TABLE `agent_uptime_stats`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_agent_date` (`agent_id`,`date_recorded`);

--
-- Indexes for table `backups`
--
ALTER TABLE `backups`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`);

--
-- Indexes for table `backup_settings`
--
ALTER TABLE `backup_settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `belongs_to_options`
--
ALTER TABLE `belongs_to_options`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`),
  ADD UNIQUE KEY `code` (`code`);

--
-- Indexes for table `branch_names`
--
ALTER TABLE `branch_names`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Indexes for table `cabinet_locations`
--
ALTER TABLE `cabinet_locations`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Indexes for table `custom_field_options`
--
ALTER TABLE `custom_field_options`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_field_option` (`field_type`,`option_key`),
  ADD KEY `idx_field_type` (`field_type`),
  ADD KEY `idx_created_by` (`created_by`);

--
-- Indexes for table `device_types`
--
ALTER TABLE `device_types`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `discounts`
--
ALTER TABLE `discounts`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Indexes for table `permissions`
--
ALTER TABLE `permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Indexes for table `ports`
--
ALTER TABLE `ports`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Indexes for table `repeaters`
--
ALTER TABLE `repeaters`
  ADD PRIMARY KEY (`id`),
  ADD KEY `tower_id` (`tower_id`);

--
-- Indexes for table `service_types`
--
ALTER TABLE `service_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`),
  ADD UNIQUE KEY `code` (`code`);

--
-- Indexes for table `setting_categories`
--
ALTER TABLE `setting_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `category_key` (`category_key`),
  ADD UNIQUE KEY `unique_category_key` (`category_key`);

--
-- Indexes for table `setting_change_log`
--
ALTER TABLE `setting_change_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_setting_id` (`setting_id`),
  ADD KEY `idx_changed_by` (`changed_by`),
  ADD KEY `idx_changed_at` (`changed_at`);

--
-- Indexes for table `simple_agents`
--
ALTER TABLE `simple_agents`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `system_settings`
--
ALTER TABLE `system_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`),
  ADD UNIQUE KEY `unique_setting_key` (`setting_key`),
  ADD KEY `idx_category_id` (`category_id`),
  ADD KEY `idx_setting_key` (`setting_key`),
  ADD KEY `idx_updated_by` (`updated_by`);

--
-- Indexes for table `towers`
--
ALTER TABLE `towers`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tower_locations`
--
ALTER TABLE `tower_locations`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`),
  ADD UNIQUE KEY `code` (`code`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD KEY `created_by` (`created_by`);

--
-- Indexes for table `user_notification_settings`
--
ALTER TABLE `user_notification_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_type` (`user_id`,`notification_type`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_notification_type` (`notification_type`);

--
-- Indexes for table `user_permissions`
--
ALTER TABLE `user_permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_permission` (`user_id`,`permission_id`),
  ADD KEY `permission_id` (`permission_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `activity_log`
--
ALTER TABLE `activity_log`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=342;

--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `agents`
--
ALTER TABLE `agents`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `agent_connection_logs`
--
ALTER TABLE `agent_connection_logs`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `agent_ips`
--
ALTER TABLE `agent_ips`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `agent_snmp_settings`
--
ALTER TABLE `agent_snmp_settings`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `agent_status_logs`
--
ALTER TABLE `agent_status_logs`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `agent_uptime_stats`
--
ALTER TABLE `agent_uptime_stats`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `backups`
--
ALTER TABLE `backups`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `backup_settings`
--
ALTER TABLE `backup_settings`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `belongs_to_options`
--
ALTER TABLE `belongs_to_options`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=29;

--
-- AUTO_INCREMENT for table `branch_names`
--
ALTER TABLE `branch_names`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `cabinet_locations`
--
ALTER TABLE `cabinet_locations`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=68;

--
-- AUTO_INCREMENT for table `custom_field_options`
--
ALTER TABLE `custom_field_options`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `device_types`
--
ALTER TABLE `device_types`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=32;

--
-- AUTO_INCREMENT for table `discounts`
--
ALTER TABLE `discounts`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `permissions`
--
ALTER TABLE `permissions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=125;

--
-- AUTO_INCREMENT for table `ports`
--
ALTER TABLE `ports`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `repeaters`
--
ALTER TABLE `repeaters`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=53;

--
-- AUTO_INCREMENT for table `service_types`
--
ALTER TABLE `service_types`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=40;

--
-- AUTO_INCREMENT for table `setting_categories`
--
ALTER TABLE `setting_categories`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `setting_change_log`
--
ALTER TABLE `setting_change_log`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `simple_agents`
--
ALTER TABLE `simple_agents`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `system_settings`
--
ALTER TABLE `system_settings`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `towers`
--
ALTER TABLE `towers`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tower_locations`
--
ALTER TABLE `tower_locations`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=49;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `user_notification_settings`
--
ALTER TABLE `user_notification_settings`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=23;

--
-- AUTO_INCREMENT for table `user_permissions`
--
ALTER TABLE `user_permissions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=180;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `activity_log`
--
ALTER TABLE `activity_log`
  ADD CONSTRAINT `fk_activity_log_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `agents`
--
ALTER TABLE `agents`
  ADD CONSTRAINT `agents_ibfk_1` FOREIGN KEY (`repeater_id`) REFERENCES `repeaters` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `agents_ibfk_2` FOREIGN KEY (`device_type_id`) REFERENCES `device_types` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `agents_ibfk_3` FOREIGN KEY (`repeater_id`) REFERENCES `repeaters` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_agents_belongs_to` FOREIGN KEY (`belongs_to_id`) REFERENCES `belongs_to_options` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_agents_branch_name` FOREIGN KEY (`branch_name_id`) REFERENCES `branch_names` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_agents_cabinet_location` FOREIGN KEY (`cabinet_location_id`) REFERENCES `cabinet_locations` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_agents_discount` FOREIGN KEY (`discount_id`) REFERENCES `discounts` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_agents_port` FOREIGN KEY (`port_id`) REFERENCES `ports` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_agents_service_type` FOREIGN KEY (`service_type_id`) REFERENCES `service_types` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_agents_tower_location` FOREIGN KEY (`tower_location_id`) REFERENCES `tower_locations` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `agent_connection_logs`
--
ALTER TABLE `agent_connection_logs`
  ADD CONSTRAINT `agent_connection_logs_ibfk_1` FOREIGN KEY (`agent_id`) REFERENCES `agents` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `agent_ips`
--
ALTER TABLE `agent_ips`
  ADD CONSTRAINT `agent_ips_ibfk_1` FOREIGN KEY (`agent_id`) REFERENCES `agents` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `agent_snmp_settings`
--
ALTER TABLE `agent_snmp_settings`
  ADD CONSTRAINT `agent_snmp_settings_ibfk_1` FOREIGN KEY (`agent_id`) REFERENCES `agents` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `agent_status_logs`
--
ALTER TABLE `agent_status_logs`
  ADD CONSTRAINT `agent_status_logs_ibfk_1` FOREIGN KEY (`agent_id`) REFERENCES `agents` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `agent_uptime_stats`
--
ALTER TABLE `agent_uptime_stats`
  ADD CONSTRAINT `agent_uptime_stats_ibfk_1` FOREIGN KEY (`agent_id`) REFERENCES `agents` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `backups`
--
ALTER TABLE `backups`
  ADD CONSTRAINT `backups_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `custom_field_options`
--
ALTER TABLE `custom_field_options`
  ADD CONSTRAINT `fk_custom_field_options_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `repeaters`
--
ALTER TABLE `repeaters`
  ADD CONSTRAINT `repeaters_ibfk_1` FOREIGN KEY (`tower_id`) REFERENCES `towers` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `setting_change_log`
--
ALTER TABLE `setting_change_log`
  ADD CONSTRAINT `fk_setting_change_log_setting` FOREIGN KEY (`setting_id`) REFERENCES `system_settings` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_setting_change_log_user` FOREIGN KEY (`changed_by`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `system_settings`
--
ALTER TABLE `system_settings`
  ADD CONSTRAINT `fk_system_settings_category` FOREIGN KEY (`category_id`) REFERENCES `setting_categories` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_system_settings_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `users`
--
ALTER TABLE `users`
  ADD CONSTRAINT `users_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `user_notification_settings`
--
ALTER TABLE `user_notification_settings`
  ADD CONSTRAINT `fk_user_notification_settings_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_permissions`
--
ALTER TABLE `user_permissions`
  ADD CONSTRAINT `user_permissions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
