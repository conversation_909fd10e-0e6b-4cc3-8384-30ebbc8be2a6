/* 
 * تنسيقات الجداول المحسنة
 * نظام إدارة المشتركين
 */

/* تنسيق الجدول الأساسي */
.table-custom {
    width: 100%;
    margin-bottom: 1rem;
    color: var(--text-primary);
    vertical-align: middle;
    border-color: var(--border-color);
    box-shadow: 0 0.125rem 0.25rem var(--shadow-color);
    border-radius: 0.5rem;
    overflow: hidden;
}

.table-custom thead th {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-weight: 600;
    border-bottom: 2px solid var(--border-color);
    padding: 0.75rem 1rem;
    text-align: right;
    position: relative;
    transition: all 0.3s ease;
}

.table-custom tbody tr {
    transition: all 0.3s ease;
}

.table-custom tbody tr:hover {
    background-color: var(--hover-bg);
    transform: scale(1.01);
}

.table-custom tbody td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

/* تنسيق الصفوف المتناوبة */
.table-striped tbody tr:nth-of-type(odd) {
    background-color: var(--bg-alternate);
}

/* تنسيق الجدول المضغوط */
.table-sm th,
.table-sm td {
    padding: 0.5rem 0.75rem;
}

/* تنسيق الجدول المستجيب */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-radius: 0.5rem;
}

/* تنسيق الأزرار داخل الجدول */
.table-custom .btn {
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
    margin: 0 0.125rem;
}

.table-custom .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

.table-custom .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* تنسيق الحالات */
.status-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 2rem;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    transition: all 0.3s ease;
}

.status-active {
    background-color: rgba(45, 206, 137, 0.2);
    color: #2dce89;
}

.status-inactive {
    background-color: rgba(231, 74, 59, 0.2);
    color: #e74a3b;
}

.status-pending {
    background-color: rgba(246, 194, 62, 0.2);
    color: #f6c23e;
}

/* تنسيق أرقام الصفحات */
.pagination {
    display: flex;
    padding-left: 0;
    list-style: none;
    border-radius: 0.375rem;
    justify-content: center;
    margin-top: 1.5rem;
}

.pagination .page-item {
    margin: 0 0.125rem;
}

.pagination .page-link {
    position: relative;
    display: block;
    padding: 0.5rem 0.75rem;
    margin-left: -1px;
    line-height: 1.25;
    color: var(--primary-color);
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    z-index: 2;
    color: var(--primary-hover);
    text-decoration: none;
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
    transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
    z-index: 3;
    color: #fff;
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.pagination .page-item.disabled .page-link {
    color: var(--text-muted);
    pointer-events: none;
    cursor: auto;
    background-color: var(--bg-primary);
    border-color: var(--border-color);
}

/* تنسيق البحث والفلترة */
.table-filter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.table-filter .form-control {
    border-radius: 0.375rem;
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.table-filter .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
}

.table-filter .btn {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
}

/* تنسيق الصفوف الفارغة */
.table-empty {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-muted);
}

.table-empty i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* تنسيق تحميل البيانات */
.table-loading {
    position: relative;
    min-height: 200px;
}

.table-loading::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.7);
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* تنسيق خاص للأجهزة المحمولة */
@media (max-width: 768px) {
    .table-responsive-stack tr {
        display: flex;
        flex-direction: column;
        border-bottom: 3px solid var(--border-color);
        margin-bottom: 1rem;
    }
    
    .table-responsive-stack td {
        display: block;
        text-align: right;
        border-bottom: none;
        position: relative;
        padding-left: 50%;
    }
    
    .table-responsive-stack td::before {
        content: attr(data-label);
        position: absolute;
        left: 0;
        width: 45%;
        padding-left: 1rem;
        font-weight: 600;
        text-align: left;
    }
    
    .table-responsive-stack thead {
        display: none;
    }
}

/* تأثيرات حركية للجداول */
.table-animated tbody tr {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* تنسيق أيقونات الترتيب */
.sort-icon {
    margin-right: 0.25rem;
    transition: all 0.3s ease;
}

th.sortable {
    cursor: pointer;
}

th.sortable:hover {
    background-color: var(--hover-bg);
}

/* تنسيق الصفوف المحددة */
.table-custom tr.selected {
    background-color: rgba(78, 115, 223, 0.1);
}

/* تنسيق خلايا الجدول الخاصة */
.table-custom .cell-primary {
    font-weight: 600;
}

.table-custom .cell-action {
    text-align: center;
    white-space: nowrap;
}

.table-custom .cell-icon {
    width: 40px;
    text-align: center;
}

.table-custom .cell-shrink {
    width: 1%;
    white-space: nowrap;
}

/* تنسيق الصفوف المعطلة */
.table-custom tr.disabled {
    opacity: 0.6;
}

/* تنسيق الأرقام في الجدول */
.table-custom .number {
    text-align: left;
    direction: ltr;
    font-family: monospace;
}

/* تنسيق التواريخ في الجدول */
.table-custom .date {
    white-space: nowrap;
}
