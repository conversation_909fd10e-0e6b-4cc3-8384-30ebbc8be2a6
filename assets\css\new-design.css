/* تصميم جديد وحديث لنظام إدارة المشتركين */

/* الخطوط والألوان الأساسية */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --success-color: #4facfe;
    --warning-color: #43e97b;
    --danger-color: #fa709a;
    --dark-color: #2c3e50;
    --light-color: #f8f9fa;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-warning: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --gradient-danger: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 20px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 30px rgba(0,0,0,0.2);
    --border-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* إعادة تعيين الخط */
* {
    font-family: 'Tajawal', 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

/* الشريط العلوي الجديد */
.modern-navbar {
    background: var(--gradient-primary);
    backdrop-filter: blur(10px);
    border: none;
    box-shadow: var(--shadow-medium);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.modern-navbar .navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: white !important;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modern-navbar .navbar-brand i {
    font-size: 2rem;
    background: linear-gradient(45deg, #fff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.modern-navbar .nav-link {
    color: rgba(255,255,255,0.9) !important;
    font-weight: 500;
    padding: 0.75rem 1.25rem !important;
    border-radius: var(--border-radius);
    margin: 0 0.25rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.modern-navbar .nav-link:hover {
    background: rgba(255,255,255,0.2);
    color: white !important;
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

.modern-navbar .nav-link.active {
    background: rgba(255,255,255,0.25);
    color: white !important;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
}

.modern-navbar .dropdown-menu {
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(10px);
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    margin-top: 0.5rem;
}

.modern-navbar .dropdown-item {
    padding: 0.75rem 1.25rem;
    transition: var(--transition);
    border-radius: 8px;
    margin: 0.25rem;
}

.modern-navbar .dropdown-item:hover {
    background: var(--gradient-primary);
    color: white;
    transform: translateX(5px);
}

/* البطاقات الحديثة */
.modern-card {
    background: rgba(255,255,255,0.9);
    backdrop-filter: blur(10px);
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    overflow: hidden;
    position: relative;
}

.modern-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.modern-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.modern-card .card-body {
    padding: 2rem;
}

.modern-card .card-title {
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

/* بطاقات الإحصائيات */
.stats-card {
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(10px);
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    overflow: hidden;
    position: relative;
    text-decoration: none;
    color: inherit;
}

.stats-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-heavy);
    text-decoration: none;
    color: inherit;
}

.stats-card .card-body {
    padding: 2rem;
    position: relative;
    z-index: 2;
}

.stats-card .stats-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
}

.stats-card .stats-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
    transform: rotate(45deg);
    transition: var(--transition);
}

.stats-card:hover .stats-icon::before {
    animation: shimmer 1.5s ease-in-out;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.stats-card.primary .stats-icon { background: var(--gradient-primary); }
.stats-card.success .stats-icon { background: var(--gradient-success); }
.stats-card.warning .stats-icon { background: var(--gradient-warning); }
.stats-card.danger .stats-icon { background: var(--gradient-danger); }
.stats-card.secondary .stats-icon { background: var(--gradient-secondary); }

.stats-card .stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.stats-card .stats-label {
    font-size: 1.1rem;
    font-weight: 500;
    color: #6c757d;
    margin-bottom: 0;
}

.stats-card .stats-change {
    font-size: 0.9rem;
    font-weight: 500;
    margin-top: 0.5rem;
}

/* الأزرار الحديثة */
.modern-btn {
    padding: 0.75rem 2rem;
    border-radius: var(--border-radius);
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.modern-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: var(--transition);
}

.modern-btn:hover::before {
    left: 100%;
}

.modern-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.modern-btn.primary {
    background: var(--gradient-primary);
    color: white;
}

.modern-btn.success {
    background: var(--gradient-success);
    color: white;
}

.modern-btn.warning {
    background: var(--gradient-warning);
    color: white;
}

.modern-btn.danger {
    background: var(--gradient-danger);
    color: white;
}

/* الجداول الحديثة */
.modern-table {
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.modern-table table {
    margin: 0;
    border: none;
}

.modern-table thead th {
    background: var(--gradient-primary);
    color: white;
    font-weight: 600;
    border: none;
    padding: 1.25rem;
    text-align: center;
}

.modern-table tbody td {
    padding: 1rem 1.25rem;
    border: none;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    vertical-align: middle;
    text-align: center;
}

.modern-table tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
    transition: var(--transition);
}

/* النماذج الحديثة */
.modern-form {
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-light);
}

.modern-form .form-control {
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    transition: var(--transition);
    background: rgba(255,255,255,0.8);
}

.modern-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: white;
}

.modern-form .form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* الرسوم المتحركة */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-up {
    animation: slideUp 0.6s ease-out;
}

.scale-in {
    animation: scaleIn 0.4s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0;
        transform: translateY(30px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from { 
        opacity: 0;
        transform: scale(0.9);
    }
    to { 
        opacity: 1;
        transform: scale(1);
    }
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .modern-navbar .navbar-brand {
        font-size: 1.2rem;
    }
    
    .stats-card .stats-number {
        font-size: 2rem;
    }
    
    .stats-card .stats-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .modern-card .card-body {
        padding: 1.5rem;
    }
}

/* تأثيرات إضافية */
.glass-effect {
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.floating {
    animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* تحسينات إضافية للتصميم */
.modern-sidebar {
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    padding: 2rem;
    margin-bottom: 2rem;
    position: sticky;
    top: 100px;
}

.modern-sidebar .sidebar-title {
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--primary-color);
}

.modern-sidebar .sidebar-item {
    padding: 0.75rem 1rem;
    margin-bottom: 0.5rem;
    border-radius: 8px;
    transition: var(--transition);
    cursor: pointer;
}

.modern-sidebar .sidebar-item:hover {
    background: var(--gradient-primary);
    color: white;
    transform: translateX(10px);
}

/* تحسين الجداول */
.modern-table tbody tr:nth-child(even) {
    background: rgba(102, 126, 234, 0.02);
}

.modern-table .table-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.modern-table .action-btn {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    font-size: 0.9rem;
}

.modern-table .action-btn:hover {
    transform: scale(1.1);
}

.modern-table .action-btn.edit {
    background: var(--gradient-warning);
    color: white;
}

.modern-table .action-btn.delete {
    background: var(--gradient-danger);
    color: white;
}

.modern-table .action-btn.view {
    background: var(--gradient-success);
    color: white;
}

/* تحسين النماذج */
.modern-form .form-group {
    margin-bottom: 1.5rem;
}

.modern-form .form-control:focus {
    transform: translateY(-2px);
}

.modern-form .form-select {
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    transition: var(--transition);
    background: rgba(255,255,255,0.8);
}

.modern-form .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: white;
}

/* تحسين الإشعارات */
.modern-alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1.25rem 1.5rem;
    margin-bottom: 1.5rem;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.modern-alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
}

.modern-alert.alert-success {
    background: rgba(75, 192, 192, 0.1);
    color: #155724;
}

.modern-alert.alert-success::before {
    background: var(--gradient-success);
}

.modern-alert.alert-danger {
    background: rgba(220, 53, 69, 0.1);
    color: #721c24;
}

.modern-alert.alert-danger::before {
    background: var(--gradient-danger);
}

.modern-alert.alert-warning {
    background: rgba(255, 193, 7, 0.1);
    color: #856404;
}

.modern-alert.alert-warning::before {
    background: var(--gradient-warning);
}

.modern-alert.alert-info {
    background: rgba(13, 202, 240, 0.1);
    color: #055160;
}

.modern-alert.alert-info::before {
    background: var(--gradient-primary);
}
