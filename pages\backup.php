<?php
require_once '../includes/init.php';
require_once '../includes/backup.class.php';

// التحقق من تسجيل الدخول
check_login();

$backup = new DatabaseBackup($conn);
$message = '';
$error = '';

// إضافة التصميم الجديد
echo "<style>
/* تأثيرات إضافية لصفحة النسخ الاحتياطية */
body {
    background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
    background-size: 400% 400%;
    animation: gradientBG 15s ease infinite;
}

@keyframes gradientBG {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.container-fluid {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    margin: 1rem;
}

.card {
    transition: all 0.3s ease !important;
    border: none !important;
    border-radius: 20px !important;
    backdrop-filter: blur(10px);
    background: rgba(255,255,255,0.95) !important;
}

.card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 15px 35px rgba(0,0,0,0.15) !important;
}

.btn {
    border-radius: 15px !important;
    padding: 0.75rem 1.5rem !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    border: none !important;
}

.btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.btn-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; }
.btn-success { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%) !important; }
.btn-warning { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%) !important; }
.btn-danger { background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%) !important; }
.btn-info { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important; }

.table {
    background: rgba(255,255,255,0.95);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1.2rem;
    font-weight: 600;
}

.table tbody tr {
    transition: all 0.3s ease;
    border: none;
}

.table tbody tr:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    transform: translateX(5px);
}

.table tbody td {
    border: none;
    padding: 1rem;
    vertical-align: middle;
}

.form-control, .form-select {
    border-radius: 15px !important;
    border: 2px solid rgba(102, 126, 234, 0.2) !important;
    padding: 0.75rem 1rem !important;
    transition: all 0.3s ease !important;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
    transform: translateY(-2px) !important;
}

.alert {
    border-radius: 15px !important;
    border: none !important;
    backdrop-filter: blur(10px);
}

.animate__fadeInUp {
    animation-duration: 0.8s;
}
</style>";

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['create_backup'])) {
        $result = $backup->createBackup();
        if ($result['success']) {
            $message = "تم إنشاء النسخة الاحتياطية بنجاح";
        } else {
            $error = $result['error'];
        }
    } 
    elseif (isset($_POST['restore_backup']) && isset($_POST['filename'])) {
        $result = $backup->restoreBackup($_POST['filename']);
        if ($result['success']) {
            $message = "تم استعادة النسخة الاحتياطية بنجاح";
        } else {
            $error = $result['error'];
        }
    }
    elseif (isset($_POST['delete_backup']) && isset($_POST['filename'])) {
        $result = $backup->deleteBackup($_POST['filename']);
        if ($result['success']) {
            $message = "تم حذف النسخة الاحتياطية بنجاح";
        } else {
            $error = $result['error'];
        }
    }
    elseif (isset($_POST['update_settings'])) {
        $result = $backup->updateSettings(
            $_POST['frequency'],
            $_POST['time'],
            (int)$_POST['retention_days']
        );
        if ($result['success']) {
            $message = "تم تحديث الإعدادات بنجاح";
        } else {
            $error = $result['error'];
        }
    }
}

// جلب قائمة النسخ الاحتياطية والإعدادات
$backups = $backup->getBackupsList();
$settings = $backup->getSettings();

// تضمين الهيدر
require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <!-- رأس الصفحة المحدث -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-lg animate__animated animate__fadeInDown"
                 style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border-radius: 20px;">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="text-white">
                            <h1 class="h2 mb-2 fw-bold">
                                <i class="fas fa-database me-3" style="font-size: 2.5rem;"></i>
                                إدارة النسخ الاحتياطية
                            </h1>
                            <p class="mb-0 opacity-75" style="font-size: 1.1rem;">
                                <i class="fas fa-shield-alt me-2"></i>
                                حماية وأمان البيانات مع النسخ الاحتياطي التلقائي
                            </p>
                        </div>
                        <div class="d-flex flex-column gap-2">
                            <form method="POST" class="d-inline">
                                <button type="submit" name="create_backup"
                                        class="btn btn-light btn-lg shadow-sm"
                                        style="border-radius: 15px; font-weight: 600;">
                                    <i class="fas fa-plus me-2"></i> إنشاء نسخة احتياطية
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسائل -->
    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeInUp" role="alert" style="border-radius: 15px;">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeInUp" role="alert" style="border-radius: 15px;">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- إعدادات النسخ الاحتياطي المحدثة -->
        <div class="col-12 col-xl-4 mb-4">
            <div class="card animate__animated animate__fadeInLeft" style="animation-delay: 0.2s;">
                <div class="card-header border-0 pb-0"
                     style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); border-radius: 20px 20px 0 0;">
                    <div class="d-flex align-items-center p-3">
                        <i class="fas fa-cogs text-white me-3" style="font-size: 1.5rem;"></i>
                        <h6 class="mb-0 text-white fw-bold">إعدادات النسخ الاحتياطي التلقائي</h6>
                    </div>
                </div>
                <div class="card-body p-4">
                    <form method="POST">
                        <div class="mb-4">
                            <label class="form-label fw-bold">
                                <i class="fas fa-clock me-2 text-primary"></i>
                                تكرار النسخ الاحتياطي
                            </label>
                            <select name="frequency" class="form-select" required>
                                <option value="daily" <?php echo $settings['frequency'] === 'daily' ? 'selected' : ''; ?>>يومي</option>
                                <option value="weekly" <?php echo $settings['frequency'] === 'weekly' ? 'selected' : ''; ?>>أسبوعي</option>
                                <option value="monthly" <?php echo $settings['frequency'] === 'monthly' ? 'selected' : ''; ?>>شهري</option>
                            </select>
                        </div>
                        <div class="mb-4">
                            <label class="form-label fw-bold">
                                <i class="fas fa-calendar-alt me-2 text-success"></i>
                                وقت النسخ الاحتياطي
                            </label>
                            <input type="time" name="time" class="form-control" value="<?php echo $settings['time'] ?? '00:00'; ?>" required>
                        </div>
                        <div class="mb-4">
                            <label class="form-label fw-bold">
                                <i class="fas fa-archive me-2 text-warning"></i>
                                مدة الاحتفاظ بالنسخ (بالأيام)
                            </label>
                            <input type="number" name="retention_days" class="form-control" value="<?php echo $settings['retention_days'] ?? 30; ?>" required min="1">
                        </div>
                        <button type="submit" name="update_settings" class="btn btn-primary w-100">
                            <i class="fas fa-save me-2"></i>
                            حفظ الإعدادات
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- قائمة النسخ الاحتياطية -->
        <div class="col-12 col-xl-8 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6>النسخ الاحتياطية المتوفرة</h6>
                    <form method="POST" class="m-0">
                        <button type="submit" name="create_backup" class="btn btn-primary btn-sm">
                            <i class="bi bi-plus-circle"></i> إنشاء نسخة جديدة
                        </button>
                    </form>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم الملف</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الحجم</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($backups as $backup): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($backup['filename']); ?></td>
                                        <td><?php echo $backup['created_at']; ?></td>
                                        <td><?php echo number_format($backup['size'] / 1024, 2); ?> KB</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="download_backup.php?file=<?php echo urlencode($backup['filename']); ?>" 
                                                   class="btn btn-sm btn-info">
                                                    <i class="bi bi-download"></i>
                                                </a>
                                                <form method="POST" class="d-inline">
                                                    <input type="hidden" name="filename" value="<?php echo htmlspecialchars($backup['filename']); ?>">
                                                    <button type="submit" name="restore_backup" 
                                                            class="btn btn-sm btn-warning" 
                                                            onclick="return confirm('هل أنت متأكد من استعادة هذه النسخة؟');">
                                                        <i class="bi bi-arrow-counterclockwise"></i>
                                                    </button>
                                                </form>
                                                <form method="POST" class="d-inline">
                                                    <input type="hidden" name="filename" value="<?php echo htmlspecialchars($backup['filename']); ?>">
                                                    <button type="submit" name="delete_backup" 
                                                            class="btn btn-sm btn-danger" 
                                                            onclick="return confirm('هل أنت متأكد من حذف هذه النسخة؟');">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                                <?php if (empty($backups)): ?>
                                    <tr>
                                        <td colspan="4" class="text-center">لا توجد نسخ احتياطية متوفرة</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
