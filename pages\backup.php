<?php
require_once '../includes/init.php';
require_once '../includes/backup.class.php';

// التحقق من تسجيل الدخول
check_login();

$backup = new DatabaseBackup($conn);
$message = '';
$error = '';

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['create_backup'])) {
        $result = $backup->createBackup();
        if ($result['success']) {
            $message = "تم إنشاء النسخة الاحتياطية بنجاح";
        } else {
            $error = $result['error'];
        }
    } 
    elseif (isset($_POST['restore_backup']) && isset($_POST['filename'])) {
        $result = $backup->restoreBackup($_POST['filename']);
        if ($result['success']) {
            $message = "تم استعادة النسخة الاحتياطية بنجاح";
        } else {
            $error = $result['error'];
        }
    }
    elseif (isset($_POST['delete_backup']) && isset($_POST['filename'])) {
        $result = $backup->deleteBackup($_POST['filename']);
        if ($result['success']) {
            $message = "تم حذف النسخة الاحتياطية بنجاح";
        } else {
            $error = $result['error'];
        }
    }
    elseif (isset($_POST['update_settings'])) {
        $result = $backup->updateSettings(
            $_POST['frequency'],
            $_POST['time'],
            (int)$_POST['retention_days']
        );
        if ($result['success']) {
            $message = "تم تحديث الإعدادات بنجاح";
        } else {
            $error = $result['error'];
        }
    }
}

// جلب قائمة النسخ الاحتياطية والإعدادات
$backups = $backup->getBackupsList();
$settings = $backup->getSettings();

// تضمين الهيدر
require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- إعدادات النسخ الاحتياطي -->
        <div class="col-12 col-xl-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6>إعدادات النسخ الاحتياطي التلقائي</h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">تكرار النسخ الاحتياطي</label>
                            <select name="frequency" class="form-select" required>
                                <option value="daily" <?php echo $settings['frequency'] === 'daily' ? 'selected' : ''; ?>>يومي</option>
                                <option value="weekly" <?php echo $settings['frequency'] === 'weekly' ? 'selected' : ''; ?>>أسبوعي</option>
                                <option value="monthly" <?php echo $settings['frequency'] === 'monthly' ? 'selected' : ''; ?>>شهري</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">وقت النسخ الاحتياطي</label>
                            <input type="time" name="time" class="form-control" value="<?php echo $settings['time'] ?? '00:00'; ?>" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">مدة الاحتفاظ بالنسخ (بالأيام)</label>
                            <input type="number" name="retention_days" class="form-control" value="<?php echo $settings['retention_days'] ?? 30; ?>" required min="1">
                        </div>
                        <button type="submit" name="update_settings" class="btn btn-primary">حفظ الإعدادات</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- قائمة النسخ الاحتياطية -->
        <div class="col-12 col-xl-8 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6>النسخ الاحتياطية المتوفرة</h6>
                    <form method="POST" class="m-0">
                        <button type="submit" name="create_backup" class="btn btn-primary btn-sm">
                            <i class="bi bi-plus-circle"></i> إنشاء نسخة جديدة
                        </button>
                    </form>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم الملف</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الحجم</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($backups as $backup): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($backup['filename']); ?></td>
                                        <td><?php echo $backup['created_at']; ?></td>
                                        <td><?php echo number_format($backup['size'] / 1024, 2); ?> KB</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="download_backup.php?file=<?php echo urlencode($backup['filename']); ?>" 
                                                   class="btn btn-sm btn-info">
                                                    <i class="bi bi-download"></i>
                                                </a>
                                                <form method="POST" class="d-inline">
                                                    <input type="hidden" name="filename" value="<?php echo htmlspecialchars($backup['filename']); ?>">
                                                    <button type="submit" name="restore_backup" 
                                                            class="btn btn-sm btn-warning" 
                                                            onclick="return confirm('هل أنت متأكد من استعادة هذه النسخة؟');">
                                                        <i class="bi bi-arrow-counterclockwise"></i>
                                                    </button>
                                                </form>
                                                <form method="POST" class="d-inline">
                                                    <input type="hidden" name="filename" value="<?php echo htmlspecialchars($backup['filename']); ?>">
                                                    <button type="submit" name="delete_backup" 
                                                            class="btn btn-sm btn-danger" 
                                                            onclick="return confirm('هل أنت متأكد من حذف هذه النسخة؟');">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                                <?php if (empty($backups)): ?>
                                    <tr>
                                        <td colspan="4" class="text-center">لا توجد نسخ احتياطية متوفرة</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
