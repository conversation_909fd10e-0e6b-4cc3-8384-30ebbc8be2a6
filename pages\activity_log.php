<?php
require_once '../includes/init.php';
require_once '../includes/header.php';

// إضافة التصميم الجديد
echo "<style>
/* خلفية ثابتة بالألوان الجديدة لصفحة سجل الأنشطة */
body {
    background: linear-gradient(135deg, #38C5E6 0%, #0B8582 50%, #01233D 100%);
    background-attachment: fixed;
    min-height: 100vh;
}

.container-fluid {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    margin: 1rem;
}

.card {
    transition: all 0.3s ease !important;
    border: none !important;
    border-radius: 20px !important;
    backdrop-filter: blur(10px);
}

.card:not(.header-card) {
    background: rgba(255,255,255,0.95) !important;
}

.header-card {
    background: transparent !important;
}

.card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 15px 35px rgba(0,0,0,0.15) !important;
}

.btn {
    border-radius: 15px !important;
    padding: 0.75rem 1.5rem !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    border: none !important;
}

.btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.btn-primary { background: linear-gradient(135deg, #38C5E6 0%, #0B8582 100%) !important; }
.btn-success { background: linear-gradient(135deg, #0B8582 0%, #01233D 100%) !important; }
.btn-warning { background: linear-gradient(135deg, #38C5E6 0%, #01233D 100%) !important; }
.btn-danger { background: linear-gradient(135deg, #01233D 0%, #0B8582 100%) !important; }
.btn-info { background: linear-gradient(135deg, #38C5E6 0%, #0B8582 100%) !important; }

.table {
    background: rgba(255,255,255,0.95);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.table thead th {
    background: linear-gradient(135deg, #01233D 0%, #0B8582 100%);
    color: white;
    border: none;
    padding: 1.2rem;
    font-weight: 600;
}

.table tbody tr {
    transition: all 0.3s ease;
    border: none;
}

.table tbody tr:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    transform: translateX(5px);
}

.table tbody td {
    border: none;
    padding: 1rem;
    vertical-align: middle;
}

.form-control, .form-select, .form-control:focus, .form-select:focus {
    border-radius: 15px !important;
    border: 2px solid rgba(102, 126, 234, 0.2) !important;
    padding: 0.75rem 1rem !important;
    transition: all 0.3s ease !important;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
    transform: translateY(-2px) !important;
}

.badge {
    border-radius: 15px !important;
    padding: 0.5rem 1rem !important;
    font-weight: 600 !important;
}

.animate__fadeInUp {
    animation-duration: 0.8s;
}
</style>";

// معالجة التصفية
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-30 days'));
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
$admin_id = isset($_GET['admin_id']) ? (int)$_GET['admin_id'] : 0;
$action_type = isset($_GET['action_type']) ? clean($_GET['action_type']) : '';

// التحقق من بنية جدول activity_log
$check_columns = $conn->query("SHOW COLUMNS FROM activity_log");
$has_user_id = false;
$has_admin_id = false;

while ($column = $check_columns->fetch_assoc()) {
    if ($column['Field'] === 'user_id') {
        $has_user_id = true;
    }
    if ($column['Field'] === 'admin_id') {
        $has_admin_id = true;
    }
}

// بناء الاستعلام حسب بنية الجدول
if ($has_user_id) {
    // النظام الجديد
    $user_column = 'user_id';
    $sql = "SELECT al.*, u.username
            FROM activity_log al
            LEFT JOIN users u ON al.user_id = u.id
            WHERE al.created_at BETWEEN ? AND ? ";
} else if ($has_admin_id) {
    // النظام القديم
    $user_column = 'admin_id';
    $sql = "SELECT al.*, u.username
            FROM activity_log al
            LEFT JOIN users u ON al.admin_id = u.id
            WHERE al.created_at BETWEEN ? AND ? ";
} else {
    // لا يوجد عمود مستخدم
    $user_column = null;
    $sql = "SELECT al.*
            FROM activity_log al
            WHERE al.created_at BETWEEN ? AND ? ";
}

if ($admin_id && $user_column) {
    $sql .= " AND al.$user_column = $admin_id";
}
if ($action_type) {
    $sql .= " AND al.action = '$action_type'";
}

$sql .= " ORDER BY al.created_at DESC LIMIT 1000";

$stmt = $conn->prepare($sql);
$start_date_with_time = $start_date . ' 00:00:00';
$end_date_with_time = $end_date . ' 23:59:59';
$stmt->bind_param("ss", $start_date_with_time, $end_date_with_time);
$stmt->execute();
$activities = $stmt->get_result();

// جلب المستخدمين للفلترة
$admins = $conn->query("SELECT id, username, full_name as name FROM users ORDER BY username");

// جلب أنواع النشاطات الفريدة
$action_types = $conn->query("SELECT DISTINCT action FROM activity_log ORDER BY action");
?>

<div class="container-fluid py-4">
    <!-- رأس الصفحة المحدث -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card header-card border-0 shadow-lg animate__animated animate__fadeInDown"
                 style="background: linear-gradient(135deg, #1a1a1a 0%, #2c2c2c 100%) !important; border-radius: 20px;">
                <div class="card-body p-4" style="background: transparent !important;">
                    <div class="d-flex justify-content-between align-items-center">
                        <div style="background: transparent !important;">
                            <h1 class="h2 mb-2 fw-bold" style="color: #ffffff !important; background: transparent !important;">
                                <i class="fas fa-history me-3" style="font-size: 2.5rem; color: #667eea;"></i>
                                سجل الأنشطة والعمليات
                            </h1>
                            <p class="mb-0" style="font-size: 1.1rem; color: #ffffff !important; background: transparent !important;">
                                <i class="fas fa-list-alt me-2" style="color: #667eea;"></i>
                                متابعة وتتبع جميع أنشطة المستخدمين في النظام
                            </p>
                        </div>
                        <div class="text-white text-center">
                            <div class="rounded-circle p-3 mb-2" style="width: 80px; height: 80px; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                <i class="fas fa-chart-line" style="font-size: 2rem; color: #ffffff;"></i>
                            </div>
                            <small style="color: #ffffff;">تقارير مفصلة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="card mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
        <div class="card-header border-0 pb-0"
             style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); border-radius: 20px 20px 0 0;">
            <div class="d-flex align-items-center p-3">
                <i class="fas fa-filter text-white me-3" style="font-size: 1.5rem;"></i>
                <h5 class="mb-0 text-white fw-bold">فلاتر البحث والتصفية</h5>
            </div>
        </div>
        <div class="card-body p-4">
            <form method="GET" class="row g-4">
                <div class="col-md-3">
                    <label class="form-label fw-bold">
                        <i class="fas fa-calendar-alt me-2 text-primary"></i>
                        من تاريخ
                    </label>
                    <input type="date" name="start_date" class="form-control" value="<?php echo $start_date; ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">
                        <i class="fas fa-calendar-check me-2 text-success"></i>
                        إلى تاريخ
                    </label>
                    <input type="date" name="end_date" class="form-control" value="<?php echo $end_date; ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">
                        <i class="fas fa-user-shield me-2 text-warning"></i>
                        المدير
                    </label>
                    <select name="admin_id" class="form-select">
                        <option value="">جميع المديرين</option>
                        <?php while ($admin = $admins->fetch_assoc()): ?>
                        <option value="<?php echo $admin['id']; ?>" <?php echo $admin_id == $admin['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($admin['username']); ?>
                        </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-bold">
                        <i class="fas fa-tasks me-2 text-info"></i>
                        نوع النشاط
                    </label>
                    <select name="action_type" class="form-select">
                        <option value="">جميع الأنشطة</option>
                        <?php while ($type = $action_types->fetch_assoc()): ?>
                        <option value="<?php echo $type['action']; ?>" <?php echo $action_type == $type['action'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($type['action']); ?>
                        </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary flex-fill">
                            <i class="fas fa-search me-1"></i> بحث
                        </button>
                        <button type="reset" class="btn btn-secondary">
                            <i class="fas fa-undo"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول الأنشطة -->
    <div class="card animate__animated animate__fadeInUp" style="animation-delay: 0.4s;">
        <div class="card-body p-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h5 class="mb-0 fw-bold">
                    <i class="fas fa-list me-2 text-primary"></i>
                    سجل الأنشطة
                </h5>
                <span class="badge bg-primary">
                    <?php echo $activities->num_rows; ?> نشاط
                </span>
            </div>

            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th class="text-center">
                                <i class="fas fa-clock me-2"></i>التاريخ والوقت
                            </th>
                            <th class="text-center">
                                <i class="fas fa-user me-2"></i>المدير
                            </th>
                            <th class="text-center">
                                <i class="fas fa-cog me-2"></i>النشاط
                            </th>
                            <th class="text-center">
                                <i class="fas fa-info-circle me-2"></i>الوصف
                            </th>
                        </tr>
                    </thead>
                <tbody>
                    <?php while ($row = $activities->fetch_assoc()): ?>
                    <tr>
                        <td><?php echo date('Y-m-d H:i:s', strtotime($row['created_at'])); ?></td>
                        <td><?php echo htmlspecialchars($row['username'] ?? 'غير معروف'); ?></td>
                        <td><?php echo htmlspecialchars($row['action']); ?></td>
                        <td><?php echo htmlspecialchars($row['description']); ?></td>
                    </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>