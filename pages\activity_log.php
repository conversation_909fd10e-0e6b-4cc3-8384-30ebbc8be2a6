<?php
require_once '../includes/header.php';

// معالجة التصفية
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-30 days'));
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
$admin_id = isset($_GET['admin_id']) ? (int)$_GET['admin_id'] : 0;
$action_type = isset($_GET['action_type']) ? clean($_GET['action_type']) : '';

// التحقق من بنية جدول activity_log
$check_columns = $conn->query("SHOW COLUMNS FROM activity_log");
$has_user_id = false;
$has_admin_id = false;

while ($column = $check_columns->fetch_assoc()) {
    if ($column['Field'] === 'user_id') {
        $has_user_id = true;
    }
    if ($column['Field'] === 'admin_id') {
        $has_admin_id = true;
    }
}

// بناء الاستعلام حسب بنية الجدول
if ($has_user_id) {
    // النظام الجديد
    $user_column = 'user_id';
    $sql = "SELECT al.*, u.username
            FROM activity_log al
            LEFT JOIN users u ON al.user_id = u.id
            WHERE al.created_at BETWEEN ? AND ? ";
} else if ($has_admin_id) {
    // النظام القديم
    $user_column = 'admin_id';
    $sql = "SELECT al.*, u.username
            FROM activity_log al
            LEFT JOIN users u ON al.admin_id = u.id
            WHERE al.created_at BETWEEN ? AND ? ";
} else {
    // لا يوجد عمود مستخدم
    $user_column = null;
    $sql = "SELECT al.*
            FROM activity_log al
            WHERE al.created_at BETWEEN ? AND ? ";
}

if ($admin_id && $user_column) {
    $sql .= " AND al.$user_column = $admin_id";
}
if ($action_type) {
    $sql .= " AND al.action = '$action_type'";
}

$sql .= " ORDER BY al.created_at DESC LIMIT 1000";

$stmt = $conn->prepare($sql);
$start_date_with_time = $start_date . ' 00:00:00';
$end_date_with_time = $end_date . ' 23:59:59';
$stmt->bind_param("ss", $start_date_with_time, $end_date_with_time);
$stmt->execute();
$activities = $stmt->get_result();

// جلب المستخدمين للفلترة
$admins = $conn->query("SELECT id, username, full_name as name FROM users ORDER BY username");

// جلب أنواع النشاطات الفريدة
$action_types = $conn->query("SELECT DISTINCT action FROM activity_log ORDER BY action");
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>سجل النشاطات</h2>
</div>

<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row">
            <div class="col-md-3 mb-3">
                <label class="form-label">من تاريخ</label>
                <input type="date" name="start_date" class="form-control" value="<?php echo $start_date; ?>">
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" name="end_date" class="form-control" value="<?php echo $end_date; ?>">
            </div>
            <div class="col-md-2 mb-3">
                <label class="form-label">المدير</label>
                <select name="admin_id" class="form-select">
                    <option value="">الكل</option>
                    <?php while ($admin = $admins->fetch_assoc()): ?>
                    <option value="<?php echo $admin['id']; ?>" <?php echo $admin_id == $admin['id'] ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($admin['username']); ?>
                    </option>
                    <?php endwhile; ?>
                </select>
            </div>
            <div class="col-md-2 mb-3">
                <label class="form-label">نوع النشاط</label>
                <select name="action_type" class="form-select">
                    <option value="">الكل</option>
                    <?php while ($type = $action_types->fetch_assoc()): ?>
                    <option value="<?php echo $type['action']; ?>" <?php echo $action_type == $type['action'] ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($type['action']); ?>
                    </option>
                    <?php endwhile; ?>
                </select>
            </div>
            <div class="col-md-2 mb-3">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary w-100">
                    <i class="bi bi-search"></i> بحث
                </button>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>التاريخ والوقت</th>
                        <th>المدير</th>
                        <th>النشاط</th>
                        <th>الوصف</th>
                    </tr>
                </thead>
                <tbody>
                    <?php while ($row = $activities->fetch_assoc()): ?>
                    <tr>
                        <td><?php echo date('Y-m-d H:i:s', strtotime($row['created_at'])); ?></td>
                        <td><?php echo htmlspecialchars($row['username'] ?? 'غير معروف'); ?></td>
                        <td><?php echo htmlspecialchars($row['action']); ?></td>
                        <td><?php echo htmlspecialchars($row['description']); ?></td>
                    </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>