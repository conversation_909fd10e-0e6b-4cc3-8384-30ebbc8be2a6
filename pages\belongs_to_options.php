<?php
require_once '../includes/init.php';
$page_title = "إدارة تابع الى";
include '../includes/header.php';

// معالجة العمليات
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        if ($_POST['action'] == 'add') {
            $name = clean($_POST['name']);
            $description = clean($_POST['description']);
            $status = clean($_POST['status']);

            // إنشاء كود تلقائي من الاسم
            $code = strtoupper(substr(preg_replace('/[^a-zA-Z0-9]/', '', $name), 0, 10)) . '_' . time();

            $sql = "INSERT INTO belongs_to_options (name, code, description, status) VALUES (?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("ssss", $name, $code, $description, $status);
            
            if ($stmt->execute()) {
                $message = "تم إضافة خيار التبعية بنجاح";
                log_activity("إضافة خيار تبعية", "تمت إضافة خيار تبعية جديد: $name");
            } else {
                $error = "حدث خطأ أثناء إضافة خيار التبعية: " . $conn->error;
            }
        }
        elseif ($_POST['action'] == 'edit') {
            $id = (int)$_POST['id'];
            $name = clean($_POST['name']);
            $description = clean($_POST['description']);
            $status = clean($_POST['status']);

            // إنشاء كود تلقائي من الاسم
            $code = strtoupper(substr(preg_replace('/[^a-zA-Z0-9]/', '', $name), 0, 10)) . '_' . $id;

            $sql = "UPDATE belongs_to_options SET name=?, code=?, description=?, status=? WHERE id=?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("ssssi", $name, $code, $description, $status, $id);
            
            if ($stmt->execute()) {
                $message = "تم تحديث خيار التبعية بنجاح";
                log_activity("تحديث خيار تبعية", "تم تحديث خيار تبعية: $name");
            } else {
                $error = "حدث خطأ أثناء تحديث خيار التبعية: " . $conn->error;
            }
        }
        elseif ($_POST['action'] == 'delete') {
            $id = (int)$_POST['id'];
            
            // جلب اسم الخيار قبل الحذف
            $name_result = $conn->query("SELECT name FROM belongs_to_options WHERE id = $id");
            $name = $name_result ? $name_result->fetch_assoc()['name'] : 'غير معروف';
            
            $sql = "DELETE FROM belongs_to_options WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $id);
            
            if ($stmt->execute()) {
                $message = "تم حذف خيار التبعية بنجاح";
                log_activity("حذف خيار تبعية", "تم حذف خيار تبعية: $name");
            } else {
                $error = "حدث خطأ أثناء حذف خيار التبعية: " . $conn->error;
            }
        }
    }
}

// جلب جميع خيارات التبعية
$sql = "SELECT * FROM belongs_to_options ORDER BY created_at DESC";
$result = $conn->query($sql);
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-diagram-3 me-2"></i>
                        إدارة تابع الى
                    </h5>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addModal">
                        <i class="bi bi-plus-circle me-2"></i>
                        إضافة خيار جديد
                    </button>
                </div>
                <div class="card-body">
                    <?php if ($message): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo $message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($error): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo $error; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الرقم</th>
                                    <th>اسم الجهة</th>
                                    <th>الوصف</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإضافة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($result && $result->num_rows > 0): ?>
                                    <?php while ($row = $result->fetch_assoc()): ?>
                                        <tr>
                                            <td><?php echo $row['id']; ?></td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($row['name']); ?></strong>
                                            </td>
                                            <td><?php echo htmlspecialchars($row['description'] ?? '-'); ?></td>
                                            <td>
                                                <?php if ($row['status'] == 'active'): ?>
                                                    <span class="badge bg-success">نشط</span>
                                                <?php elseif ($row['status'] == 'suspended'): ?>
                                                    <span class="badge bg-warning">معلق</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">غير نشط</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?php echo date('Y-m-d H:i', strtotime($row['created_at'])); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary"
                                                            onclick="editBelongsToOption(<?php echo htmlspecialchars(json_encode($row)); ?>)">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                                            onclick="deleteBelongsToOption(<?php echo $row['id']; ?>, '<?php echo htmlspecialchars($row['name']); ?>')">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="6" class="text-center text-muted py-4">
                                            <i class="bi bi-inbox display-4"></i>
                                            <p class="mt-2">لا توجد خيارات تبعية مضافة بعد</p>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة خيار جديد -->
<div class="modal fade" id="addModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <form method="POST">
                <input type="hidden" name="action" value="add">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة خيار تبعية جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">اسم الجهة <span class="text-danger">*</span></label>
                        <input type="text" name="name" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <textarea name="description" class="form-control" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الحالة</label>
                        <select name="status" class="form-select">
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                            <option value="suspended">معلق</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal تعديل خيار -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <form method="POST">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="id" id="edit_id">
                <div class="modal-header">
                    <h5 class="modal-title">تعديل خيار التبعية</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">اسم الجهة <span class="text-danger">*</span></label>
                        <input type="text" name="name" id="edit_name" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <textarea name="description" id="edit_description" class="form-control" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الحالة</label>
                        <select name="status" id="edit_status" class="form-select">
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                            <option value="suspended">معلق</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal حذف خيار -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="id" id="delete_id">
                <div class="modal-header">
                    <h5 class="modal-title text-danger">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <i class="bi bi-exclamation-triangle text-warning display-4"></i>
                        <h4 class="mt-3">هل أنت متأكد؟</h4>
                        <p>سيتم حذف خيار التبعية: <strong id="delete_name"></strong></p>
                        <p class="text-muted">لا يمكن التراجع عن هذا الإجراء</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">حذف</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editBelongsToOption(option) {
    document.getElementById('edit_id').value = option.id;
    document.getElementById('edit_name').value = option.name;
    document.getElementById('edit_description').value = option.description || '';
    document.getElementById('edit_status').value = option.status;

    new bootstrap.Modal(document.getElementById('editModal')).show();
}

function deleteBelongsToOption(id, name) {
    document.getElementById('delete_id').value = id;
    document.getElementById('delete_name').textContent = name;
    
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>

<?php require_once '../includes/footer.php'; ?>
