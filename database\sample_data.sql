-- بيانات تجريبية للنظام المنظف
-- يمكن تشغيل هذا الملف بعد إنشاء قاعدة البيانات

-- إدراج مستخدم إداري افتراضي
INSERT INTO `users` (`id`, `username`, `password`, `full_name`, `email`, `role`, `is_active`, `created_at`) VALUES
(1, 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'المدير العام', '<EMAIL>', 'admin', 1, NOW());

-- إدراج صلاحيات أساسية
INSERT INTO `permissions` (`id`, `name`, `display_name`, `category`, `description`) VALUES
(1, 'agents.view', 'عرض المشتركين', 'agents', 'عرض قائمة المشتركين'),
(2, 'agents.create', 'إضافة مشترك', 'agents', 'إضافة مشترك جديد'),
(3, 'agents.edit', 'تعديل المشتركين', 'agents', 'تعديل بيانات المشتركين'),
(4, 'agents.delete', 'حذف المشتركين', 'agents', 'حذف المشتركين'),
(5, 'reports.view', 'عرض التقارير', 'reports', 'عرض التقارير والإحصائيات'),
(6, 'reports.export', 'تصدير البيانات', 'reports', 'تصدير البيانات إلى ملفات'),
(7, 'users.manage', 'إدارة المستخدمين', 'users', 'إدارة مستخدمي النظام'),
(8, 'permissions.manage', 'إدارة الصلاحيات', 'permissions', 'منح وإلغاء الصلاحيات');

-- منح جميع الصلاحيات للمدير
INSERT INTO `user_permissions` (`user_id`, `permission_id`, `granted`) VALUES
(1, 1, 1), (1, 2, 1), (1, 3, 1), (1, 4, 1),
(1, 5, 1), (1, 6, 1), (1, 7, 1), (1, 8, 1);

-- إدراج أنواع خدمات أساسية
INSERT INTO `service_types` (`id`, `name`, `code`, `description`, `price`, `status`) VALUES
(1, 'فايبر منزلي', 'FIBER_HOME', 'خدمة فايبر للمنازل', 25000.00, 'active'),
(2, 'فايبر تجاري', 'FIBER_BUSINESS', 'خدمة فايبر للشركات', 50000.00, 'active'),
(3, 'وايرلس', 'WIRELESS', 'خدمة إنترنت لاسلكي', 20000.00, 'active');

-- إدراج فروع أساسية
INSERT INTO `branch_names` (`id`, `name`, `description`, `status`) VALUES
(1, 'المكتب الرئيسي', 'المكتب الرئيسي للشركة', 'active'),
(2, 'فرع النجف', 'فرع محافظة النجف', 'active'),
(3, 'فرع كربلاء', 'فرع محافظة كربلاء', 'active');

-- إدراج مواقع كابينات أساسية
INSERT INTO `cabinet_locations` (`id`, `name`, `description`, `status`) VALUES
(1, 'كابينة الوسط', 'كابينة وسط المدينة', 'active'),
(2, 'كابينة الشمال', 'كابينة شمال المدينة', 'active'),
(3, 'كابينة الجنوب', 'كابينة جنوب المدينة', 'active');

-- إدراج مواقع أبراج أساسية
INSERT INTO `tower_locations` (`id`, `name`, `code`, `description`, `status`) VALUES
(1, 'برج الوسط', 'TOWER_CENTER', 'برج وسط المدينة', 'active'),
(2, 'برج الشمال', 'TOWER_NORTH', 'برج شمال المدينة', 'active'),
(3, 'برج الجنوب', 'TOWER_SOUTH', 'برج جنوب المدينة', 'active');

-- إدراج منافذ أساسية
INSERT INTO `ports` (`id`, `name`, `description`, `status`) VALUES
(1, 'Port 1', 'المنفذ الأول', 'active'),
(2, 'Port 2', 'المنفذ الثاني', 'active'),
(3, 'Port 3', 'المنفذ الثالث', 'active');

-- إدراج خصومات أساسية
INSERT INTO `discounts` (`id`, `name`, `description`, `status`) VALUES
(1, 'بدون خصم', 'بدون خصم', 'active'),
(2, 'خصم 10%', 'خصم عشرة بالمئة', 'active'),
(3, 'خصم VIP', 'خصم للعملاء المميزين', 'active');

-- إدراج خيارات التبعية
INSERT INTO `belongs_to_options` (`id`, `name`, `code`, `description`, `status`) VALUES
(1, 'مباشر', 'DIRECT', 'مشترك مباشر', 'active'),
(2, 'وكيل', 'AGENT', 'عبر وكيل', 'active'),
(3, 'شركة', 'COMPANY', 'عبر شركة', 'active');

-- إدراج مشترك تجريبي
INSERT INTO `agents` (`id`, `agent_name`, `phone_number`, `point_name`, `username`, `password`, `unms_status`, `bill_price`, `service_type_id`, `branch_name_id`, `cabinet_location_id`, `tower_location_id`, `port_id`, `discount_id`, `belongs_to_id`, `created_at`) VALUES
(1, 'فندق القمر', '07714403444', 'NajafOnline10', '667@NajafOnline10', '1234', 'active', 33750.00, 1, 2, 1, 1, 1, 2, 1, NOW());

-- إدراج عنوان IP للمشترك التجريبي
INSERT INTO `agent_ips` (`id`, `agent_id`, `ip_address`, `ip_description`, `is_primary`, `created_at`) VALUES
(1, 1, '************', 'MASTER', 1, NOW());
