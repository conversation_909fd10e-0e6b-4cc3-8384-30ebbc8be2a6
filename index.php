<?php
require_once 'config/database.php';
require_once 'includes/init.php';

if (isset($_SESSION['user_id'])) {
    header("Location: pages/dashboard.php");
    exit();
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = $conn->real_escape_string($_POST['username']);
    $password = $_POST['password'];

    $sql = "SELECT id, username, full_name, password, role, is_active FROM users WHERE username = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows == 1) {
        $user = $result->fetch_assoc();
        if (!$user['is_active']) {
            $error = 'هذا الحساب غير مفعل';
        } elseif (password_verify($password, $user['password'])) {
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['full_name'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['user_role'] = $user['role'];

            // تحديث آخر تسجيل دخول
            $update = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
            $update->bind_param("i", $user['id']);
            $update->execute();

            header("Location: pages/dashboard.php");
            exit();
        } else {
            $error = 'كلمة المرور غير صحيحة';
        }
    } else {
        $error = 'اسم المستخدم غير موجود';
    }
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة المشتركين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4e73df;
            --primary-hover: #2e59d9;
            --secondary-color: #1cc88a;
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --text-primary: #212529;
            --text-secondary: #6c757d;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --border-radius: 12px;
        }
        
        [data-theme="dark"] {
            --primary-color: #4e73df;
            --primary-hover: #2e59d9;
            --secondary-color: #1cc88a;
            --bg-primary: #1a1a2e;
            --bg-secondary: #16213e;
            --text-primary: #e6e6e6;
            --text-secondary: #adb5bd;
            --shadow-color: rgba(0, 0, 0, 0.5);
        }
        
        body {
            background: var(--bg-secondary);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Tajawal', sans-serif;
            transition: all 0.3s ease;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        .login-page {
            display: flex;
            width: 100%;
            min-height: 100vh;
            align-items: stretch;
        }
        
        .login-sidebar {
            flex: 1;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            padding: 2rem;
            position: relative;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
        }
        
        .login-sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('https://images.unsplash.com/photo-1557683316-973673baf926?ixlib=rb-4.0.3&auto=format&fit=crop&w=1080&q=80') no-repeat center center;
            background-size: cover;
            opacity: 0.2;
            z-index: 0;
        }
        
        .sidebar-content {
            position: relative;
            z-index: 1;
            text-align: center;
            max-width: 90%;
        }
        
        .login-main {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 2rem;
            background-color: var(--bg-primary);
        }
        
        .login-container {
            background: var(--bg-primary);
            padding: 3rem;
            border-radius: var(--border-radius);
            box-shadow: 0 10px 30px var(--shadow-color);
            width: 100%;
            max-width: 500px;
            transition: all 0.3s ease;
            animation: fadeIn 1s;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }
        
        .login-header i {
            font-size: 4rem;
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .login-header h2 {
            color: var(--text-primary);
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .login-header p {
            color: var(--text-secondary);
            margin-bottom: 2rem;
        }
        
        .btn-login {
            width: 100%;
            padding: 0.9rem;
            font-size: 1.1rem;
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            border-radius: 50px;
            transition: all 0.3s ease;
            font-weight: 600;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }
        
        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: all 0.6s;
            z-index: -1;
        }
        
        .btn-login:hover::before {
            left: 100%;
        }
        
        .btn-login:hover {
            background-color: var(--primary-hover);
            border-color: var(--primary-hover);
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        
        .form-floating {
            margin-bottom: 1.5rem;
        }
        
        .form-floating > .form-control {
            padding: 1.2rem 1rem 0.5rem;
            height: calc(3.5rem + 2px);
            line-height: 1.25;
            background-color: var(--bg-secondary);
            border: 2px solid rgba(0, 0, 0, 0.1);
            color: var(--text-primary);
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
        }
        
        .form-floating > label {
            padding: 1rem;
            color: var(--text-secondary);
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.15);
        }
        
        .input-group-text {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            border-radius: var(--border-radius);
        }
        
        .alert {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: 0 0.125rem 0.25rem var(--shadow-color);
            animation: shake 0.5s;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .alert-danger {
            background-color: rgba(231, 74, 59, 0.1);
            color: #e74a3b;
            border-right: 4px solid #e74a3b;
        }
        
        .sidebar-logo {
            font-size: 2.5rem;
            margin-bottom: 1.5rem;
            color: white;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        .sidebar-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: white;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        .sidebar-text {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .features-list {
            text-align: right;
            list-style: none;
            padding: 0;
            margin-bottom: 2rem;
        }
        
        .features-list li {
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }
        
        .features-list i {
            margin-left: 0.75rem;
            color: var(--secondary-color);
            font-size: 1.2rem;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }
        
        .theme-toggle {
            position: absolute;
            top: 20px;
            left: 20px;
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            cursor: pointer;
            z-index: 10;
            backdrop-filter: blur(5px);
        }
        
        .theme-toggle:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: rotate(15deg);
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
        }
        
        .theme-toggle i {
            font-size: 1.25rem;
        }
        
        /* تحسينات للأجهزة المحمولة */
        @media (max-width: 991.98px) {
            .login-page {
                flex-direction: column;
            }
            
            .login-sidebar {
                min-height: 300px;
            }
            
            .sidebar-title {
                font-size: 2rem;
            }
            
            .features-list {
                display: none;
            }
        }
    </style>
</head>
<body>
    <button class="theme-toggle" id="theme-toggle">
        <i class="bi bi-moon-fill"></i>
    </button>
    
    <div class="login-page">
        <!-- الشريط الجانبي -->
        <div class="login-sidebar">
            <div class="sidebar-content">
                <div class="sidebar-logo animate__animated animate__fadeIn">
                    <i class="bi bi-people-fill"></i>
                </div>
                <h1 class="sidebar-title animate__animated animate__fadeIn">نظام إدارة المشتركين</h1>
                <p class="sidebar-text animate__animated animate__fadeIn">منصة متكاملة لإدارة المشتركين ومتابعة أدائهم بكفاءة عالية</p>
                
                <ul class="features-list">
                    <li class="animate__animated animate__slideIn">
                        <span>إدارة كاملة للوكلاء والعملاء</span>
                        <i class="bi bi-check-circle-fill"></i>
                    </li>
                    <li class="animate__animated animate__slideIn" style="animation-delay: 0.1s;">
                        <span>تقارير وإحصائيات متقدمة</span>
                        <i class="bi bi-check-circle-fill"></i>
                    </li>
                    <li class="animate__animated animate__slideIn" style="animation-delay: 0.2s;">
                        <span>واجهة سهلة الاستخدام</span>
                        <i class="bi bi-check-circle-fill"></i>
                    </li>
                    <li class="animate__animated animate__slideIn" style="animation-delay: 0.3s;">
                        <span>حماية وأمان للبيانات</span>
                        <i class="bi bi-check-circle-fill"></i>
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- القسم الرئيسي -->
        <div class="login-main">
            <div class="login-container animate__animated animate__fadeIn">
                <div class="login-header">
                    <i class="bi bi-shield-lock-fill"></i>
                    <h2>تسجيل الدخول</h2>
                    <p>أدخل بيانات تسجيل الدخول للوصول إلى لوحة التحكم</p>
                </div>

                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <form method="POST" action="">
                    <div class="form-floating mb-4">
                        <input type="text" name="username" class="form-control" id="username" placeholder="اسم المستخدم" required autofocus>
                        <label for="username">اسم المستخدم</label>
                    </div>

                    <div class="form-floating mb-4">
                        <input type="password" name="password" class="form-control" id="password" placeholder="كلمة المرور" required>
                        <label for="password">كلمة المرور</label>
                    </div>

                    <button type="submit" class="btn btn-primary btn-login mt-3">
                        <i class="bi bi-box-arrow-in-right me-2"></i>
                        تسجيل الدخول
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من وجود تفضيل الوضع المظلم في localStorage
            if (localStorage.getItem('theme') === 'dark') {
                document.documentElement.setAttribute('data-theme', 'dark');
                document.getElementById('theme-toggle').innerHTML = '<i class="bi bi-sun-fill"></i>';
            }
            
            // إضافة حدث النقر لزر تبديل الوضع
            document.getElementById('theme-toggle').addEventListener('click', function() {
                if (document.documentElement.getAttribute('data-theme') === 'dark') {
                    document.documentElement.removeAttribute('data-theme');
                    localStorage.setItem('theme', 'light');
                    this.innerHTML = '<i class="bi bi-moon-fill"></i>';
                } else {
                    document.documentElement.setAttribute('data-theme', 'dark');
                    localStorage.setItem('theme', 'dark');
                    this.innerHTML = '<i class="bi bi-sun-fill"></i>';
                }
            });
            
            // إضافة تأخير للعناصر المتحركة
            const animatedItems = document.querySelectorAll('.animate__animated');
            animatedItems.forEach((item, index) => {
                if (!item.style.animationDelay && !item.classList.contains('animate__slideIn')) {
                    item.style.animationDelay = `${index * 0.1}s`;
                }
            });
            
            // تركيز حقل اسم المستخدم عند تحميل الصفحة
            setTimeout(() => {
                const usernameInput = document.getElementById('username');
                if (usernameInput) {
                    usernameInput.focus();
                }
            }, 500);
        });
    </script>
</body>
</html>
