            </div>
        </div>
    </div>

    <!-- Footer حديث -->
    <footer class="mt-5 py-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-network-wired me-3" style="font-size: 2rem;"></i>
                        <div>
                            <h5 class="mb-1">نظام إدارة المشتركين</h5>
                            <p class="mb-0 opacity-75">نظام متطور لإدارة المشتركين والخدمات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 text-md-end mt-3 mt-md-0">
                    <div class="d-flex justify-content-md-end justify-content-center align-items-center gap-3">
                        <span class="opacity-75">تم التطوير بواسطة فريق التقنية</span>
                        <div class="d-flex gap-2">
                            <div class="bg-white bg-opacity-20 rounded-circle p-2">
                                <i class="fas fa-code"></i>
                            </div>
                            <div class="bg-white bg-opacity-20 rounded-circle p-2">
                                <i class="fas fa-heart"></i>
                            </div>
                        </div>
                    </div>
                    <small class="opacity-75 d-block mt-2">
                        © <?php echo date('Y'); ?> جميع الحقوق محفوظة
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // تهيئة AOS للرسوم المتحركة
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            mirror: false
        });

        // تأثيرات إضافية للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير hover للبطاقات
            const cards = document.querySelectorAll('.modern-card, .stats-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // تأثير النقر للأزرار
            const buttons = document.querySelectorAll('.modern-btn');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    let ripple = document.createElement('span');
                    ripple.classList.add('ripple');
                    this.appendChild(ripple);

                    let x = e.clientX - e.target.offsetLeft;
                    let y = e.clientY - e.target.offsetTop;

                    ripple.style.left = `${x}px`;
                    ripple.style.top = `${y}px`;

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // تحديث الوقت في الوقت الفعلي
            function updateTime() {
                const now = new Date();
                const timeString = now.toLocaleString('ar-SA', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
                
                const timeElement = document.getElementById('current-time');
                if (timeElement) {
                    timeElement.textContent = timeString;
                }
            }

            // تحديث الوقت كل دقيقة
            updateTime();
            setInterval(updateTime, 60000);

            // تأثير التحميل للجداول
            const tables = document.querySelectorAll('.modern-table');
            tables.forEach(table => {
                const rows = table.querySelectorAll('tbody tr');
                rows.forEach((row, index) => {
                    row.style.opacity = '0';
                    row.style.transform = 'translateY(20px)';
                    
                    setTimeout(() => {
                        row.style.transition = 'all 0.3s ease';
                        row.style.opacity = '1';
                        row.style.transform = 'translateY(0)';
                    }, index * 100);
                });
            });

            // تأثير الكتابة للعناوين
            function typeWriter(element, text, speed = 100) {
                let i = 0;
                element.innerHTML = '';
                
                function type() {
                    if (i < text.length) {
                        element.innerHTML += text.charAt(i);
                        i++;
                        setTimeout(type, speed);
                    }
                }
                type();
            }

            // تطبيق تأثير الكتابة على العناوين الرئيسية
            const mainTitle = document.querySelector('.page-title');
            if (mainTitle) {
                const originalText = mainTitle.textContent;
                typeWriter(mainTitle, originalText, 80);
            }

            // تأثير التدرج المتحرك للخلفية
            function animateGradient() {
                const body = document.body;
                let angle = 135;
                
                setInterval(() => {
                    angle = (angle + 1) % 360;
                    body.style.background = `linear-gradient(${angle}deg, #f5f7fa 0%, #c3cfe2 100%)`;
                }, 100);
            }

            // تفعيل الرسوم المتحركة للخلفية (اختياري)
            // animateGradient();

            // تحسين تجربة التنقل
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // إضافة تأثير التحميل
                    if (!this.href.includes('#')) {
                        const loader = document.createElement('div');
                        loader.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center';
                        loader.style.background = 'rgba(255,255,255,0.9)';
                        loader.style.zIndex = '9999';
                        loader.innerHTML = `
                            <div class="text-center">
                                <div class="spinner-border text-primary mb-3" role="status">
                                    <span class="visually-hidden">جاري التحميل...</span>
                                </div>
                                <p class="text-muted">جاري تحميل الصفحة...</p>
                            </div>
                        `;
                        document.body.appendChild(loader);
                    }
                });
            });

            // إخفاء شاشة التحميل عند اكتمال تحميل الصفحة
            window.addEventListener('load', function() {
                const loader = document.querySelector('.position-fixed');
                if (loader) {
                    loader.style.opacity = '0';
                    setTimeout(() => loader.remove(), 300);
                }
            });
        });

        // دالة لإظهار الإشعارات المخصصة
        function showNotification(message, type = 'info', duration = 5000) {
            const notification = document.createElement('div');
            notification.className = `toast notification-toast show`;
            notification.setAttribute('role', 'alert');
            
            const iconMap = {
                'success': 'bi-check-circle-fill',
                'error': 'bi-exclamation-triangle-fill',
                'warning': 'bi-exclamation-triangle-fill',
                'info': 'bi-info-circle-fill'
            };
            
            const colorMap = {
                'success': 'bg-success',
                'error': 'bg-danger',
                'warning': 'bg-warning',
                'info': 'bg-info'
            };
            
            notification.innerHTML = `
                <div class="toast-header ${colorMap[type]} text-white">
                    <i class="${iconMap[type]} me-2"></i>
                    <strong class="me-auto">إشعار</strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">${message}</div>
            `;
            
            document.body.appendChild(notification);
            
            // إزالة الإشعار تلقائياً
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 300);
            }, duration);
        }

        // تحسين الأداء - تحميل الصور بشكل تدريجي
        function lazyLoadImages() {
            const images = document.querySelectorAll('img[data-src]');
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            images.forEach(img => imageObserver.observe(img));
        }

        // تفعيل التحميل التدريجي للصور
        lazyLoadImages();
    </script>

    <style>
        /* تأثيرات إضافية */
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255,255,255,0.6);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        .lazy {
            opacity: 0;
            transition: opacity 0.3s;
        }

        .lazy.loaded {
            opacity: 1;
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .notification-toast {
                right: 10px;
                left: 10px;
                min-width: auto;
            }
            
            .page-title {
                font-size: 2rem;
            }
            
            .quick-actions {
                flex-direction: column;
            }
            
            .quick-action-btn {
                min-width: auto;
            }
        }
    </style>
</body>
</html>
