<?php
ob_start();
require_once '../includes/init.php';
$page_title = "تعديل بيانات المشترك";
include '../includes/header.php';

// التحقق من وجود معرف المشترك
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error'] = "معرف المشترك غير صالح";
    header("Location: agents.php");
    exit();
}

$agent_id = (int)$_GET['id'];

// تأكد من أن المعرف قيمة عددية صالحة
if ($agent_id <= 0) {
    $_SESSION['error'] = "معرف المشترك يجب أن يكون أكبر من صفر";
    header("Location: agents.php");
    exit();
}

// جلب بيانات المشترك
$sql = "SELECT * FROM agents WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $agent_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    $_SESSION['error'] = "المشترك غير موجود";
    header("Location: agents.php");
    exit();
}

$agent = $result->fetch_assoc();

// جلب عناوين IP للوكيل
$ip_sql = "SELECT * FROM agent_ips WHERE agent_id = ? ORDER BY is_primary DESC, id ASC";
$ip_stmt = $conn->prepare($ip_sql);
$ip_stmt->bind_param("i", $agent_id);
$ip_stmt->execute();
$ip_result = $ip_stmt->get_result();
$agent_ips = $ip_result->fetch_all(MYSQLI_ASSOC);

// معالجة تحديث البيانات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $agent_name = clean($_POST['agent_name']);
    $phone_number = clean($_POST['phone_number']);
    $secondary_phone_number = clean($_POST['secondary_phone_number'] ?? '');
    $point_name = clean($_POST['point_name']);
    $username = clean($_POST['username']);
    $password = clean($_POST['password']);
    $rp_sub = clean($_POST['rp_sub']);
    $port = clean($_POST['port']);
    $device_ownership = clean($_POST['device_ownership']);
    $unms_status = clean($_POST['unms_status']);
    $notes = clean($_POST['notes']);
    $mac_address = clean($_POST['mac_address'] ?? '');
    $ssid = clean($_POST['ssid'] ?? '');
    $ip_address = clean($_POST['ip_address'] ?? '');

    // إضافة الحقول الجديدة
    $serial_number = clean($_POST['serial_number'] ?? '');
    $sn_onu = clean($_POST['sn_onu'] ?? '');
    $sector_number = clean($_POST['sector_number'] ?? '');
    $bill_price = !empty($_POST['bill_price']) ? (float)$_POST['bill_price'] : null;
    $port_id = !empty($_POST['port_id']) ? (int)$_POST['port_id'] : null;
    $cabinet_location_id = !empty($_POST['cabinet_location_id']) ? (int)$_POST['cabinet_location_id'] : null;
    $discount_id = !empty($_POST['discount_id']) ? (int)$_POST['discount_id'] : null;
    $service_type_id = !empty($_POST['service_type_id']) ? (int)$_POST['service_type_id'] : null;
    $belongs_to_id = !empty($_POST['belongs_to_id']) ? (int)$_POST['belongs_to_id'] : null;
    $branch_name_id = !empty($_POST['branch_name_id']) ? (int)$_POST['branch_name_id'] : null;
    $tower_location_id = !empty($_POST['tower_location_id']) ? (int)$_POST['tower_location_id'] : null;
    
    try {
        $conn->begin_transaction();
        
        // تهريب القيم لمنع هجمات SQL injection
        $agent_name = $conn->real_escape_string($agent_name);
        $phone_number = $conn->real_escape_string($phone_number);
        $secondary_phone_number = $conn->real_escape_string($secondary_phone_number);
        $point_name = $conn->real_escape_string($point_name);
        $username = $conn->real_escape_string($username);
        $rp_sub = $conn->real_escape_string($rp_sub);
        $port = $conn->real_escape_string($port);
        $device_ownership = $conn->real_escape_string($device_ownership);
        $unms_status = $conn->real_escape_string($unms_status);
        $notes = $conn->real_escape_string($notes);
        $ip_address = $conn->real_escape_string($ip_address);
        $mac_address = $conn->real_escape_string($mac_address);
        $ssid = $conn->real_escape_string($ssid);
        $serial_number = $conn->real_escape_string($serial_number);
        $sn_onu = $conn->real_escape_string($sn_onu);
        $sector_number = $conn->real_escape_string($sector_number);
        
        // استعلام تحديث مباشر
$direct_sql = "UPDATE agents SET
        agent_name = '{$agent_name}',
        phone_number = '{$phone_number}',
        secondary_phone_number = '{$secondary_phone_number}',
        point_name = '{$point_name}',
        username = '{$username}',
        rp_sub = '{$rp_sub}',
        port = '{$port}',
        device_ownership = '{$device_ownership}',
        unms_status = '{$unms_status}',
        notes = '{$notes}',
        ip_address = '{$ip_address}',
        mac_address = '{$mac_address}',
        ssid = '{$ssid}',
        serial_number = '{$serial_number}',
        sn_onu = '{$sn_onu}',
        sector_number = '{$sector_number}',
        bill_price = " . ($bill_price !== null ? $bill_price : "NULL") . ",
        port_id = " . ($port_id !== null ? $port_id : "NULL") . ",
        cabinet_location_id = " . ($cabinet_location_id !== null ? $cabinet_location_id : "NULL") . ",
        discount_id = " . ($discount_id !== null ? $discount_id : "NULL") . ",
        service_type_id = " . ($service_type_id !== null ? $service_type_id : "NULL") . ",
        belongs_to_id = " . ($belongs_to_id !== null ? $belongs_to_id : "NULL") . ",
        branch_name_id = " . ($branch_name_id !== null ? $branch_name_id : "NULL") . ",
        tower_location_id = " . ($tower_location_id !== null ? $tower_location_id : "NULL") . "
        WHERE id = {$agent_id}";
        
        // تنفيذ الاستعلام
        $result = $conn->query($direct_sql);
        
        if ($result === false) {
            throw new Exception("فشل تحديث بيانات المشترك: " . $conn->error);
        }
        
        // تحديث كلمة المرور إذا تم تغييرها
        if (!empty($password)) {
            $password_escaped = $conn->real_escape_string($password);
            $pwd_sql = "UPDATE agents SET password = '{$password_escaped}' WHERE id = {$agent_id}";
            
            $pwd_result = $conn->query($pwd_sql);
            
            if ($pwd_result === false) {
                throw new Exception("فشل تحديث كلمة المرور: " . $conn->error);
            }
        }
        
        // معالجة عناوين IP
        // حذف جميع عناوين IP القديمة
        $delete_ips_sql = "DELETE FROM agent_ips WHERE agent_id = {$agent_id}";
        
        $delete_result = $conn->query($delete_ips_sql);
        
        if ($delete_result === false) {
            throw new Exception("فشل حذف عناوين IP القديمة: " . $conn->error);
        }

        // إضافة عناوين IP الجديدة
        if (!empty($_POST['ip_addresses']) && is_array($_POST['ip_addresses'])) {
            foreach ($_POST['ip_addresses'] as $index => $ip) {
                if (empty($ip['address'])) continue;
                
                $is_primary = isset($_POST['primary_ip']) && $_POST['primary_ip'] == $index ? 1 : 0;
                $ip_address = $conn->real_escape_string($ip['address']);
                $ip_description = $conn->real_escape_string(clean($ip['description']));
                $ip_username = $conn->real_escape_string(clean($ip['username'] ?? ''));
                $ip_password = $conn->real_escape_string(clean($ip['password'] ?? ''));
                
                $insert_ip_sql = "INSERT INTO agent_ips (agent_id, ip_address, ip_description, username, password, is_primary) 
                                   VALUES ({$agent_id}, '{$ip_address}', '{$ip_description}', '{$ip_username}', '{$ip_password}', {$is_primary})";
                
                $insert_result = $conn->query($insert_ip_sql);
                
                if ($insert_result === false) {
                    throw new Exception("فشل إضافة عنوان IP: {$ip_address}. الخطأ: " . $conn->error);
                }
            }
        }
        
        $conn->commit();
        
        // تسجيل النشاط
        log_activity("تعديل مشترك", "تم تعديل بيانات المشترك: {$agent_name} (ID: {$agent_id})");

        // تعيين رسالة النجاح وإعادة التوجيه
        $_SESSION['success'] = "تم تحديث بيانات المشترك بنجاح";
        header("Location: agents.php");
        exit();
    } catch (Exception $e) {
        $conn->rollback();
        $error = "حدث خطأ: " . $e->getMessage();
    }
}



?>


<div class="container-fluid py-4">
    <!-- رأس الصفحة مع أزرار الإجراءات -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="bi bi-pencil-square me-2"></i>
            تعديل بيانات المشترك: <?php echo htmlspecialchars($agent['agent_name']); ?>
        </h1>
        
        <div>
            <a href="agent_details.php?id=<?php echo $agent_id; ?>" class="btn btn-info me-2">
                <i class="bi bi-eye-fill me-1"></i> عرض التفاصيل
            </a>
            <a href="agents.php" class="btn btn-secondary">
                <i class="bi bi-arrow-right me-1"></i> العودة للقائمة
            </a>
        </div>
    </div>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeIn" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <form method="POST" action="" id="editAgentForm">
        <!-- بطاقة المعلومات الأساسية -->
        <div class="card mb-4 animate__animated animate__fadeIn">
            <div class="card-header bg-light">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle-fill me-2"></i>
                    المعلومات الأساسية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold required">اسم المشترك</label>
                        <input type="text" name="agent_name" class="form-control" required 
                               value="<?php echo htmlspecialchars($agent['agent_name']); ?>">
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label fw-bold">رقم الهاتف</label>
                                <input type="text" name="phone_number" class="form-control"
                                       value="<?php echo htmlspecialchars($agent['phone_number']); ?>">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">رقم الهاتف الثانوي</label>
                                <input type="text" name="secondary_phone_number" class="form-control"
                                       value="<?php echo htmlspecialchars($agent['secondary_phone_number'] ?? ''); ?>">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">العنوان</label>
                        <input type="text" name="point_name" class="form-control"
                               value="<?php echo htmlspecialchars($agent['point_name']); ?>">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">الحالة</label>
                        <select name="unms_status" class="form-select">
                            <option value="active" <?php echo $agent['unms_status'] == 'active' ? 'selected' : ''; ?>>نشط</option>
                            <option value="inactive" <?php echo $agent['unms_status'] == 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                        </select>
                    </div>
                </div>


            </div>
        </div>

        <!-- بطاقة معلومات الاتصال -->
        <div class="card mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.1s">
            <div class="card-header bg-light">
                <h5 class="mb-0">
                    <i class="bi bi-wifi me-2"></i>
                    معلومات الاتصال
                </h5>
            </div>
            <div class="card-body">
                <!-- قسم عناوين IP -->
                <div class="mb-4">
                    <label class="form-label fw-bold d-flex justify-content-between">
                        <span>عناوين IP</span>
                        <button type="button" class="btn btn-sm btn-primary" id="addIpAddress">
                            <i class="bi bi-plus-circle-fill me-1"></i> إضافة عنوان IP
                        </button>
                    </label>
                    <div id="ipAddressContainer">
                        <!-- سيتم إضافة حقول عناوين IP هنا بواسطة JavaScript -->
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">اسم المستخدم</label>
                        <input type="text" name="username" class="form-control"
                               value="<?php echo htmlspecialchars($agent['username']); ?>">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">كلمة المرور</label>
                        <div class="input-group">
                            <input type="password" name="password" id="password" class="form-control" placeholder="اترك فارغاً للإبقاء على كلمة المرور الحالية">
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                <i class="bi bi-eye-fill"></i>
                            </button>
                        </div>
                        <div class="form-text">اترك هذا الحقل فارغاً للإبقاء على كلمة المرور الحالية</div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">RP-SUB</label>
                        <input type="text" name="rp_sub" class="form-control"
                               value="<?php echo htmlspecialchars($agent['rp_sub']); ?>">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">MAC Address</label>
                        <input type="text" name="mac_address" class="form-control"
                               value="<?php echo htmlspecialchars($agent['mac_address'] ?? ''); ?>">
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="card mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.3s">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="bi bi-plus-circle-fill me-2"></i>
                    معلومات إضافية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">تسلسل</label>
                        <input type="text" name="serial_number" class="form-control"
                               value="<?php echo htmlspecialchars($agent['serial_number'] ?? ''); ?>">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">SN ONU</label>
                        <input type="text" name="sn_onu" class="form-control"
                               value="<?php echo htmlspecialchars($agent['sn_onu'] ?? ''); ?>">
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">رقم السكتر</label>
                        <input type="text" name="sector_number" class="form-control"
                               value="<?php echo htmlspecialchars($agent['sector_number'] ?? ''); ?>">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">سعر فواتير</label>
                        <div class="input-group">
                            <input type="number" step="0.01" name="bill_price" class="form-control"
                                   value="<?php echo htmlspecialchars($agent['bill_price'] ?? ''); ?>">
                            <span class="input-group-text">دينار</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- القوائم المنسدلة -->
        <div class="card mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.35s">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0">
                    <i class="bi bi-list-ul me-2"></i>
                    خيارات النظام
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">Port</label>
                        <select name="port_id" class="form-select">
                            <option value="">اختر Port</option>
                            <?php
                            $ports_result = $conn->query("SELECT id, name FROM ports WHERE status = 'active' ORDER BY name");
                            if ($ports_result) {
                                while ($port = $ports_result->fetch_assoc()) {
                                    $selected = ($agent['port_id'] == $port['id']) ? 'selected' : '';
                                    echo "<option value='{$port['id']}' $selected>" . htmlspecialchars($port['name']) . "</option>";
                                }
                            }
                            ?>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">مكان كابينة</label>
                        <select name="cabinet_location_id" class="form-select">
                            <option value="">اختر مكان كابينة</option>
                            <?php
                            $cabinets_result = $conn->query("SELECT id, name FROM cabinet_locations WHERE status = 'active' ORDER BY name");
                            if ($cabinets_result) {
                                while ($cabinet = $cabinets_result->fetch_assoc()) {
                                    $selected = ($agent['cabinet_location_id'] == $cabinet['id']) ? 'selected' : '';
                                    echo "<option value='{$cabinet['id']}' $selected>" . htmlspecialchars($cabinet['name']) . "</option>";
                                }
                            }
                            ?>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">الخصم</label>
                        <select name="discount_id" class="form-select">
                            <option value="">اختر الخصم</option>
                            <?php
                            $discounts_result = $conn->query("SELECT id, name FROM discounts WHERE status = 'active' ORDER BY name");
                            if ($discounts_result) {
                                while ($discount = $discounts_result->fetch_assoc()) {
                                    $selected = ($agent['discount_id'] == $discount['id']) ? 'selected' : '';
                                    echo "<option value='{$discount['id']}' $selected>" . htmlspecialchars($discount['name']) . "</option>";
                                }
                            }
                            ?>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">نوع الخدمة</label>
                        <select name="service_type_id" class="form-select">
                            <option value="">اختر نوع الخدمة</option>
                            <?php
                            $services_result = $conn->query("SELECT id, name FROM service_types WHERE status = 'active' ORDER BY name");
                            if ($services_result) {
                                while ($service = $services_result->fetch_assoc()) {
                                    $selected = ($agent['service_type_id'] == $service['id']) ? 'selected' : '';
                                    echo "<option value='{$service['id']}' $selected>" . htmlspecialchars($service['name']) . "</option>";
                                }
                            }
                            ?>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">تابع الى</label>
                        <select name="belongs_to_id" class="form-select">
                            <option value="">اختر تابع الى</option>
                            <?php
                            $belongs_result = $conn->query("SELECT id, name FROM belongs_to_options WHERE status = 'active' ORDER BY name");
                            if ($belongs_result) {
                                while ($belongs = $belongs_result->fetch_assoc()) {
                                    $selected = ($agent['belongs_to_id'] == $belongs['id']) ? 'selected' : '';
                                    echo "<option value='{$belongs['id']}' $selected>" . htmlspecialchars($belongs['name']) . "</option>";
                                }
                            }
                            ?>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">اسم الفرع</label>
                        <select name="branch_name_id" class="form-select">
                            <option value="">اختر اسم الفرع</option>
                            <?php
                            $branches_result = $conn->query("SELECT id, name FROM branch_names WHERE status = 'active' ORDER BY name");
                            if ($branches_result) {
                                while ($branch = $branches_result->fetch_assoc()) {
                                    $selected = ($agent['branch_name_id'] == $branch['id']) ? 'selected' : '';
                                    echo "<option value='{$branch['id']}' $selected>" . htmlspecialchars($branch['name']) . "</option>";
                                }
                            }
                            ?>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">مكان البرج</label>
                        <select name="tower_location_id" class="form-select">
                            <option value="">اختر مكان البرج</option>
                            <?php
                            $towers_result = $conn->query("SELECT id, name FROM tower_locations WHERE status = 'active' ORDER BY name");
                            if ($towers_result) {
                                while ($tower = $towers_result->fetch_assoc()) {
                                    $selected = ($agent['tower_location_id'] == $tower['id']) ? 'selected' : '';
                                    echo "<option value='{$tower['id']}' $selected>" . htmlspecialchars($tower['name']) . "</option>";
                                }
                            }
                            ?>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- بطاقة معلومات الشبكة -->
        <div class="card mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.2s">
            <div class="card-header bg-light">
                <h5 class="mb-0">
                    <i class="bi bi-hdd-network-fill me-2"></i>
                    معلومات الشبكة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">المنفذ</label>
                        <input type="text" name="port" class="form-control"
                               value="<?php echo htmlspecialchars($agent['port']); ?>">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">SSID</label>
                        <input type="text" name="ssid" class="form-control"
                               value="<?php echo htmlspecialchars($agent['ssid'] ?? ''); ?>">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">ملكية الجهاز</label>
                        <input type="text" name="device_ownership" class="form-control"
                               value="<?php echo htmlspecialchars($agent['device_ownership']); ?>">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">ملاحظات على المشترك</label>
                        <textarea name="notes" class="form-control" rows="3"><?php echo htmlspecialchars($agent['notes']); ?></textarea>
                    </div>
                </div>
            </div>
        </div>



        <!-- أزرار الإرسال -->
        <div class="d-flex justify-content-center mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.4s">
            <button type="submit" class="btn btn-primary btn-lg px-5 me-2">
                <i class="bi bi-check-circle-fill me-2"></i> حفظ التغييرات
            </button>
            <a href="agents.php" class="btn btn-secondary btn-lg px-5">
                <i class="bi bi-x-circle-fill me-2"></i> إلغاء
            </a>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    
    // إضافة عنوان IP
    let ipCounter = 0;
    const existingIps = <?php echo json_encode($agent_ips); ?>;
    
    document.getElementById('addIpAddress').addEventListener('click', function() {
        addIpAddressField();
    });
    
    function addIpAddressField(ipData = null) {
        const container = document.getElementById('ipAddressContainer');
        const row = document.createElement('div');
        row.className = 'card mb-2 ip-address-row';
        
        const ipAddress = ipData ? ipData.ip_address : '';
        const ipDescription = ipData ? ipData.ip_description : '';
        const isPrimary = ipData ? ipData.is_primary == 1 : false;
        
        const ipUsername = ipData ? ipData.username || '' : '';
        const ipPassword = ipData ? ipData.password || '' : '';
        
        row.innerHTML = `
            <div class="card-body">
                <div class="row mb-2">
                    <div class="col-md-5">
                        <div class="form-floating mb-2">
                            <input type="text" name="ip_addresses[${ipCounter}][address]" class="form-control" id="ipAddress${ipCounter}" placeholder="عنوان IP" value="${ipAddress}">
                            <label for="ipAddress${ipCounter}">عنوان IP</label>
                        </div>
                    </div>
                    <div class="col-md-5">
                        <div class="form-floating mb-2">
                            <input type="text" name="ip_addresses[${ipCounter}][description]" class="form-control" id="ipDescription${ipCounter}" placeholder="وصف العنوان" value="${ipDescription}">
                            <label for="ipDescription${ipCounter}">وصف العنوان (اختياري)</label>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="d-flex h-100 align-items-center justify-content-around">
                            <div class="form-check">
                                <input class="form-check-input primary-ip-radio" type="radio" name="primary_ip" id="primaryIp${ipCounter}" value="${ipCounter}" ${isPrimary ? 'checked' : ''}>
                                <label class="form-check-label" for="primaryIp${ipCounter}">
                                    رئيسي
                                </label>
                            </div>
                            <button type="button" class="btn btn-outline-danger btn-sm remove-ip" data-index="${ipCounter}">
                                <i class="bi bi-trash-fill"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <!-- حقول اسم المستخدم وكلمة المرور -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-floating mb-2">
                            <input type="text" name="ip_addresses[${ipCounter}][username]" class="form-control" id="ipUsername${ipCounter}" placeholder="اسم المستخدم" value="${ipUsername}">
                            <label for="ipUsername${ipCounter}">اسم المستخدم</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="input-group">
                            <div class="form-floating flex-grow-1">
                                <input type="password" name="ip_addresses[${ipCounter}][password]" class="form-control" id="ipPassword${ipCounter}" placeholder="كلمة المرور" value="${ipPassword}">
                                <label for="ipPassword${ipCounter}">كلمة المرور</label>
                            </div>
                            <button class="btn btn-outline-secondary" type="button" onclick="toggleIpPassword(${ipCounter})">
                                <i class="bi bi-eye-fill" id="ipPasswordToggleIcon${ipCounter}"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        container.appendChild(row);
        
        // إضافة حدث لزر الحذف
        row.querySelector('.remove-ip').addEventListener('click', function() {
            container.removeChild(row);
            
            // إذا تم حذف العنوان الرئيسي، حدد أول عنوان متبقي كرئيسي
            const wasChecked = this.parentNode.querySelector('input[type="radio"]').checked;
            if (wasChecked) {
                const firstRadio = document.querySelector('.primary-ip-radio');
                if (firstRadio) {
                    firstRadio.checked = true;
                }
            }
        });
        
        ipCounter++;
    }
    
    // إضافة عناوين IP الموجودة
    if (existingIps.length > 0) {
        existingIps.forEach(function(ip) {
            addIpAddressField(ip);
        });
    } else {
        // إضافة حقل IP فارغ إذا لم تكن هناك عناوين IP
        addIpAddressField();
    }
});

// دالة لإظهار/إخفاء كلمة المرور
function togglePassword() {
    const passwordField = document.getElementById('password');
    const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
    passwordField.setAttribute('type', type);
    
    // تغيير أيقونة الزر
    const icon = document.querySelector('button[onclick="togglePassword()"] i');
    if (type === 'text') {
        icon.classList.remove('bi-eye-fill');
        icon.classList.add('bi-eye-slash-fill');
    } else {
        icon.classList.remove('bi-eye-slash-fill');
        icon.classList.add('bi-eye-fill');
    }
}

// دالة لإظهار/إخفاء كلمة المرور لعناوين IP
function toggleIpPassword(index) {
    const passwordField = document.getElementById(`ipPassword${index}`);
    const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
    passwordField.setAttribute('type', type);
    
    // تغيير أيقونة الزر
    const icon = document.getElementById(`ipPasswordToggleIcon${index}`);
    if (type === 'text') {
        icon.classList.remove('bi-eye-fill');
        icon.classList.add('bi-eye-slash-fill');
    } else {
        icon.classList.remove('bi-eye-slash-fill');
        icon.classList.add('bi-eye-fill');
    }
}
</script>

<?php include '../includes/footer.php'; ?>