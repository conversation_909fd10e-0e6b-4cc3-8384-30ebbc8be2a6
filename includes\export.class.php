<?php
class ExportManager {
    private $conn;
    private $columns = [
        // Agent Information
        'agent_name' => 'اسم المشترك',
        'phone_number' => 'رقم الهاتف',
        'job_title' => 'المسمى الوظيفي',
        'point_name' => 'اسم النقطة',
        'device_function' => 'وظيفة الجهاز',
        'rp_sub' => 'RP Sub',
        'switch_name' => 'اسم السويتش',
        'port' => 'المنفذ',
        'device_ownership' => 'ملكية الجهاز',
        'unms_status' => 'حالة UNMS',
        'last_status' => 'الحالة الأخيرة',
        'last_status_change' => 'آخر تغيير للحالة',
        'notes' => 'ملاحظات على المشترك',
        
        // Location Information
        'latitude' => 'خط العرض',
        'longitude' => 'خط الطول',
        'coverage_radius' => 'نطاق التغطية',
        
        // IP Information
        'primary_ip' => 'عنوان IP الرئيسي',
        'additional_ips' => 'عناوين IP الإضافية',
        'ip_statuses' => 'حالة IP',
        'last_ping_times' => 'آخر وقت ping',
        
        // Timestamps
        'created_at' => 'تاريخ الإنشاء'
    ];

    public function __construct($conn) {
        $this->conn = $conn;
    }

    private function getAgentsData($filters = []) {
        $sql = "SELECT 
                    a.*,
                    GROUP_CONCAT(
                        DISTINCT
                        CASE 
                            WHEN ai.is_primary = 1 THEN ai.ip_address
                            ELSE NULL
                        END
                    ) as primary_ip,
                    GROUP_CONCAT(
                        DISTINCT
                        CASE 
                            WHEN ai.is_primary = 0 THEN ai.ip_address
                            ELSE NULL
                        END
                        SEPARATOR ', '
                    ) as additional_ips,
                    GROUP_CONCAT(
                        DISTINCT
                        CONCAT(ai.ip_address, ': ', 
                            CASE 
                                WHEN ai.last_ping_status = 1 THEN 'متصل'
                                WHEN ai.last_ping_status = 0 THEN 'غير متصل'
                                ELSE 'غير معروف'
                            END
                        )
                        SEPARATOR ' | '
                    ) as ip_statuses,
                    GROUP_CONCAT(
                        DISTINCT
                        CONCAT(ai.ip_address, ': ', 
                            IFNULL(DATE_FORMAT(ai.last_ping_time, '%Y-%m-%d %H:%i'), 'N/A')
                        )
                        SEPARATOR ' | '
                    ) as last_ping_times
                FROM agents a
                LEFT JOIN agent_ips ai ON a.id = ai.agent_id
                WHERE 1=1";

        if (!empty($filters['status'])) {
            $sql .= " AND a.last_status = '" . $this->conn->real_escape_string($filters['status']) . "'";
        }
        if (!empty($filters['device_type'])) {
            $sql .= " AND a.device_type_id = " . (int)$filters['device_type'];
        }

        $sql .= " GROUP BY a.id ORDER BY a.created_at DESC";
        
        $result = $this->conn->query($sql);
        if (!$result) {
            throw new Exception("Error executing query: " . $this->conn->error);
        }
        return $result->fetch_all(MYSQLI_ASSOC);
    }

    public function exportToCSV($filters = []) {
        $filename = 'agents_export_' . date('Y-m-d_H-i-s') . '.csv';
        $filepath = dirname(__DIR__) . '/exports/' . $filename;
        
        if (!is_dir(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }
        
        $fp = fopen($filepath, 'w');
        if ($fp === false) {
            throw new Exception("Could not create export file");
        }
        
        // Add BOM for Excel Arabic support
        fprintf($fp, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // Write headers
        fputcsv($fp, array_values($this->columns));
        
        try {
            $data = $this->getAgentsData($filters);
            foreach ($data as $row) {
                $exportRow = [];
                foreach ($this->columns as $field => $title) {
                    $value = $row[$field] ?? '';
                    
                    // Format timestamps
                    if (in_array($field, ['created_at', 'last_status_change']) && $value) {
                        $value = date('Y-m-d H:i', strtotime($value));
                    }
                    
                    // Format status
                    if ($field === 'last_status') {
                        $value = $value === 'online' ? 'متصل' : 'غير متصل';
                    }
                    
                    if ($field === 'unms_status') {
                        $value = $value === 'active' ? 'نشط' : 'غير نشط';
                    }
                    
                    $exportRow[] = $value;
                }
                fputcsv($fp, $exportRow);
            }
        } catch (Exception $e) {
            fclose($fp);
            throw $e;
        }
        
        fclose($fp);
        return $filename;
    }
}
