<?php
class ExportManager {
    private $conn;
    private $columns = [
        // Agent Information
        'agent_name' => 'اسم المشترك',
        'phone_number' => 'رقم الهاتف',
        'secondary_phone_number' => 'رقم الهاتف الثانوي',
        'point_name' => 'العنوان',
        'unms_status' => 'الحالة',
        'username' => 'اسم المستخدم',
        'password' => 'كلمة المرور',
        'rp_sub' => 'RP-SUB',
        'mac_address' => 'MAC Address',
        'port' => 'المنفذ',
        'ssid' => 'SSID',
        'device_ownership' => 'ملكية الجهاز',
        'notes' => 'ملاحظات على المشترك',
        'serial_number' => 'تسلسل',
        'sn_onu' => 'SN ONU',
        'sector_number' => 'رقم السكتر',
        'bill_price' => 'سعر فواتير',

        // Related Tables
        'port_name' => 'Port',
        'cabinet_location_name' => 'مكان كابينة',
        'discount_name' => 'الخصم',
        'service_type_name' => 'نوع الخدمة',
        'belongs_to_name' => 'تابع الى',
        'branch_name' => 'اسم الفرع',
        'tower_location_name' => 'مكان البرج',

        // IP Information
        'primary_ip' => 'عنوان IP الرئيسي',
        'additional_ips' => 'عناوين IP الإضافية',
        'ip_statuses' => 'حالة IP',
        'last_ping_times' => 'آخر وقت ping',

        // Timestamps
        'created_at' => 'تاريخ الإنشاء',
        'updated_at' => 'تاريخ التحديث'
    ];

    public function __construct($conn) {
        $this->conn = $conn;
    }

    private function getAgentsData($filters = []) {
        $sql = "SELECT
                    a.*,
                    p.name as port_name,
                    cl.name as cabinet_location_name,
                    d.name as discount_name,
                    st.name as service_type_name,
                    bto.name as belongs_to_name,
                    bn.name as branch_name,
                    tl.name as tower_location_name,
                    GROUP_CONCAT(
                        DISTINCT
                        CASE
                            WHEN ai.is_primary = 1 THEN ai.ip_address
                            ELSE NULL
                        END
                    ) as primary_ip,
                    GROUP_CONCAT(
                        DISTINCT
                        CASE
                            WHEN ai.is_primary = 0 THEN ai.ip_address
                            ELSE NULL
                        END
                        SEPARATOR ', '
                    ) as additional_ips,
                    GROUP_CONCAT(
                        DISTINCT
                        CONCAT(ai.ip_address, ': ',
                            CASE
                                WHEN ai.last_ping_status = 1 THEN 'متصل'
                                WHEN ai.last_ping_status = 0 THEN 'غير متصل'
                                ELSE 'غير معروف'
                            END
                        )
                        SEPARATOR ' | '
                    ) as ip_statuses,
                    GROUP_CONCAT(
                        DISTINCT
                        CONCAT(ai.ip_address, ': ',
                            IFNULL(DATE_FORMAT(ai.last_ping_time, '%Y-%m-%d %H:%i'), 'N/A')
                        )
                        SEPARATOR ' | '
                    ) as last_ping_times
                FROM agents a
                LEFT JOIN agent_ips ai ON a.id = ai.agent_id
                LEFT JOIN ports p ON a.port_id = p.id
                LEFT JOIN cabinet_locations cl ON a.cabinet_location_id = cl.id
                LEFT JOIN discounts d ON a.discount_id = d.id
                LEFT JOIN service_types st ON a.service_type_id = st.id
                LEFT JOIN belongs_to_options bto ON a.belongs_to_id = bto.id
                LEFT JOIN branch_names bn ON a.branch_name_id = bn.id
                LEFT JOIN tower_locations tl ON a.tower_location_id = tl.id
                WHERE 1=1";

        if (!empty($filters['status'])) {
            $sql .= " AND a.unms_status = '" . $this->conn->real_escape_string($filters['status']) . "'";
        }
        if (!empty($filters['service_type'])) {
            $sql .= " AND a.service_type_id = " . (int)$filters['service_type'];
        }
        if (!empty($filters['branch_name'])) {
            $sql .= " AND a.branch_name_id = " . (int)$filters['branch_name'];
        }
        if (!empty($filters['tower_location'])) {
            $sql .= " AND a.tower_location_id = " . (int)$filters['tower_location'];
        }
        if (!empty($filters['cabinet_location'])) {
            $sql .= " AND a.cabinet_location_id = " . (int)$filters['cabinet_location'];
        }

        $sql .= " GROUP BY a.id ORDER BY a.created_at DESC";
        
        $result = $this->conn->query($sql);
        if (!$result) {
            throw new Exception("Error executing query: " . $this->conn->error);
        }
        return $result->fetch_all(MYSQLI_ASSOC);
    }

    public function exportToCSV($filters = []) {
        $filename = 'agents_export_' . date('Y-m-d_H-i-s') . '.csv';
        $filepath = dirname(__DIR__) . '/exports/' . $filename;
        
        if (!is_dir(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }
        
        $fp = fopen($filepath, 'w');
        if ($fp === false) {
            throw new Exception("Could not create export file");
        }
        
        // Add BOM for Excel Arabic support
        fprintf($fp, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // Write headers
        fputcsv($fp, array_values($this->columns));
        
        try {
            $data = $this->getAgentsData($filters);
            foreach ($data as $row) {
                $exportRow = [];
                foreach ($this->columns as $field => $title) {
                    $value = $row[$field] ?? '';
                    
                    // Format timestamps
                    if (in_array($field, ['created_at', 'last_status_change']) && $value) {
                        $value = date('Y-m-d H:i', strtotime($value));
                    }
                    
                    // Format status
                    if ($field === 'last_status') {
                        $value = $value === 'online' ? 'متصل' : 'غير متصل';
                    }
                    
                    if ($field === 'unms_status') {
                        $value = $value === 'active' ? 'نشط' : 'غير نشط';
                    }
                    
                    $exportRow[] = $value;
                }
                fputcsv($fp, $exportRow);
            }
        } catch (Exception $e) {
            fclose($fp);
            throw $e;
        }
        
        fclose($fp);
        return $filename;
    }
}
