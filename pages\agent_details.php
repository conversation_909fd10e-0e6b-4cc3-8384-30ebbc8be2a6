<?php
require_once '../includes/init.php';
$page_title = "تفاصيل المشترك";
include '../includes/header.php';

// التحقق من وجود معرف المشترك
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: agents.php');
    exit;
}

$agent_id = intval($_GET['id']);

// جلب تفاصيل المشترك
$sql = "SELECT a.*,
               p.name as port_name,
               cl.name as cabinet_location_name,
               d.name as discount_name,
               st.name as service_type_name,
               bto.name as belongs_to_name,
               bn.name as branch_name,
               tl.name as tower_location_name
        FROM agents a
        LEFT JOIN ports p ON a.port_id = p.id
        LEFT JOIN cabinet_locations cl ON a.cabinet_location_id = cl.id
        LEFT JOIN discounts d ON a.discount_id = d.id
        LEFT JOIN service_types st ON a.service_type_id = st.id
        LEFT JOIN belongs_to_options bto ON a.belongs_to_id = bto.id
        LEFT JOIN branch_names bn ON a.branch_name_id = bn.id
        LEFT JOIN tower_locations tl ON a.tower_location_id = tl.id
        WHERE a.id = ?";

$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $agent_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header('Location: agents.php');
    exit;
}

$agent = $result->fetch_assoc();

// جلب عناوين IP للوكيل
$ip_sql = "SELECT * FROM agent_ips WHERE agent_id = ? ORDER BY is_primary DESC, id ASC";
$ip_stmt = $conn->prepare($ip_sql);
$ip_stmt->bind_param("i", $agent_id);
$ip_stmt->execute();
$ip_result = $ip_stmt->get_result();
$agent_ips = $ip_result->fetch_all(MYSQLI_ASSOC);

?>

<div class="container-fluid py-4">
    <!-- رأس الصفحة مع أزرار الإجراءات -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="bi bi-person-vcard me-2"></i>
            تفاصيل المشترك: <?php echo htmlspecialchars($agent['agent_name']); ?>
        </h1>
        
        <div class="d-flex gap-2">
            <?php if (check_permission('can_edit_agents')): ?>
            <a href="edit_agent.php?id=<?php echo $agent_id; ?>" class="btn btn-primary">
                <i class="bi bi-pencil-fill me-1"></i> تعديل
            </a>
            <?php endif; ?>
            
            <a href="agents.php" class="btn btn-secondary">
                <i class="bi bi-arrow-right me-1"></i> العودة للقائمة
            </a>
        </div>
    </div>

    <!-- بطاقة المعلومات الرئيسية -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card animate__animated animate__fadeIn">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="bi bi-info-circle-fill me-2"></i>
                        المعلومات الأساسية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- العمود الأول للمعلومات -->
                        <div class="col-md-6">
                            <table class="table table-custom table-striped">
                                <tr>
                                    <th width="30%">اسم المشترك</th>
                                    <td class="fw-bold"><?php echo htmlspecialchars($agent['agent_name']); ?></td>
                                </tr>
                                <tr>
                                    <th>رقم الهاتف</th>
                                    <td class="number"><?php echo htmlspecialchars($agent['phone_number']); ?></td>
                                </tr>
                                <?php if (!empty($agent['secondary_phone_number'])): ?>
                                <tr>
                                    <th>رقم الهاتف الثانوي</th>
                                    <td class="number"><?php echo htmlspecialchars($agent['secondary_phone_number']); ?></td>
                                </tr>
                                <?php endif; ?>
                                <tr>
                                    <th>العنوان</th>
                                    <td><?php echo htmlspecialchars($agent['point_name']); ?></td>
                                </tr>
                                <tr>
                                    <th>الحالة</th>
                                    <td>
                                        <?php if ($agent['unms_status'] == 'active'): ?>
                                            <span class="status-badge status-active">نشط</span>
                                        <?php else: ?>
                                            <span class="status-badge status-inactive">غير نشط</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php if (!empty($agent['serial_number'])): ?>
                                <tr>
                                    <th>التسلسل</th>
                                    <td><?php echo htmlspecialchars($agent['serial_number']); ?></td>
                                </tr>
                                <?php endif; ?>
                                <?php if (!empty($agent['sn_onu'])): ?>
                                <tr>
                                    <th>SN ONU</th>
                                    <td><?php echo htmlspecialchars($agent['sn_onu']); ?></td>
                                </tr>
                                <?php endif; ?>
                                <?php if (!empty($agent['sector_number'])): ?>
                                <tr>
                                    <th>رقم السكتر</th>
                                    <td><?php echo htmlspecialchars($agent['sector_number']); ?></td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                        
                        <!-- العمود الثاني للمعلومات -->
                        <div class="col-md-6">
                            <table class="table table-custom table-striped">
                                <?php if (!empty($agent['bill_price'])): ?>
                                <tr>
                                    <th width="30%">سعر الفاتورة</th>
                                    <td><?php echo number_format($agent['bill_price'], 0) . ' دينار'; ?></td>
                                </tr>
                                <?php endif; ?>
                                <tr>
                                    <th>ملكية الجهاز</th>
                                    <td><?php echo htmlspecialchars($agent['device_ownership'] ?? '-'); ?></td>
                                </tr>
                                <tr>
                                    <th>تاريخ الإضافة</th>
                                    <td class="date"><?php echo date('Y-m-d', strtotime($agent['created_at'] ?? '1970-01-01')); ?></td>
                                </tr>
                                <tr>
                                    <th>آخر تحديث</th>
                                    <td class="date"><?php echo !empty($agent['updated_at']) ? date('Y-m-d', strtotime($agent['updated_at'])) : date('Y-m-d', strtotime($agent['created_at'] ?? '1970-01-01')); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- بطاقة خيارات النظام -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card animate__animated animate__fadeIn" style="animation-delay: 0.15s">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-list-ul me-2"></i>
                        خيارات النظام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- العمود الأول -->
                        <div class="col-md-6">
                            <table class="table table-custom table-striped">
                                <?php if (!empty($agent['port_name'])): ?>
                                <tr>
                                    <th width="30%">Port</th>
                                    <td><?php echo htmlspecialchars($agent['port_name']); ?></td>
                                </tr>
                                <?php endif; ?>
                                <?php if (!empty($agent['cabinet_location_name'])): ?>
                                <tr>
                                    <th>مكان الكابينة</th>
                                    <td><?php echo htmlspecialchars($agent['cabinet_location_name']); ?></td>
                                </tr>
                                <?php endif; ?>
                                <?php if (!empty($agent['discount_name'])): ?>
                                <tr>
                                    <th>الخصم</th>
                                    <td><?php echo htmlspecialchars($agent['discount_name']); ?></td>
                                </tr>
                                <?php endif; ?>
                                <?php if (!empty($agent['service_type_name'])): ?>
                                <tr>
                                    <th>نوع الخدمة</th>
                                    <td><?php echo htmlspecialchars($agent['service_type_name']); ?></td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>

                        <!-- العمود الثاني -->
                        <div class="col-md-6">
                            <table class="table table-custom table-striped">
                                <?php if (!empty($agent['belongs_to_name'])): ?>
                                <tr>
                                    <th width="30%">تابع الى</th>
                                    <td><?php echo htmlspecialchars($agent['belongs_to_name']); ?></td>
                                </tr>
                                <?php endif; ?>
                                <?php if (!empty($agent['branch_name'])): ?>
                                <tr>
                                    <th>اسم الفرع</th>
                                    <td><?php echo htmlspecialchars($agent['branch_name']); ?></td>
                                </tr>
                                <?php endif; ?>
                                <?php if (!empty($agent['tower_location_name'])): ?>
                                <tr>
                                    <th>مكان البرج</th>
                                    <td><?php echo htmlspecialchars($agent['tower_location_name']); ?></td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- بطاقة معلومات الاتصال -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card animate__animated animate__fadeIn" style="animation-delay: 0.1s">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="bi bi-wifi me-2"></i>
                        معلومات الاتصال
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-custom table-striped">
                        <tr>
                            <th width="30%">عناوين IP</th>
                            <td>
                                <?php if (count($agent_ips) > 0): ?>
                                    <?php foreach ($agent_ips as $ip): ?>
                                        <div class="mb-3 p-2 border rounded">
                                            <div class="d-flex align-items-center mb-1">
                                                <span class="status-badge <?php echo $ip['is_primary'] ? 'status-active' : 'status-secondary'; ?> me-2">
                                                    <?php echo htmlspecialchars($ip['ip_address']); ?>
                                                    <?php if ($ip['is_primary']): ?>
                                                        <i class="bi bi-star-fill ms-1" title="عنوان IP رئيسي"></i>
                                                    <?php endif; ?>
                                                </span>
                                            </div>
                                            
                                            <?php if (!empty($ip['ip_description'])): ?>
                                                <div class="small text-muted mb-2">
                                                    <i class="bi bi-info-circle me-1"></i>
                                                    <?php echo htmlspecialchars($ip['ip_description']); ?>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <?php if (!empty($ip['username']) || !empty($ip['password'])): ?>
                                                <div class="row g-2 mt-1">
                                                    <?php if (!empty($ip['username'])): ?>
                                                    <div class="col-md-6">
                                                        <div class="input-group input-group-sm">
                                                            <span class="input-group-text bg-light">
                                                                <i class="bi bi-person-fill"></i>
                                                            </span>
                                                            <input type="text" class="form-control form-control-sm" value="<?php echo htmlspecialchars($ip['username']); ?>" readonly>
                                                        </div>
                                                    </div>
                                                    <?php endif; ?>
                                                    
                                                    <?php if (!empty($ip['password'])): ?>
                                                    <div class="col-md-6">
                                                        <div class="input-group input-group-sm">
                                                            <span class="input-group-text bg-light">
                                                                <i class="bi bi-key-fill"></i>
                                                            </span>
                                                            <input type="password" class="form-control form-control-sm ip-password" 
                                                                id="ipPassword<?php echo $ip['id']; ?>" value="<?php echo htmlspecialchars($ip['password']); ?>" readonly>
                                                            <button class="btn btn-outline-secondary btn-sm" type="button" 
                                                                    onclick="toggleIpPassword(<?php echo $ip['id']; ?>)">
                                                                <i class="bi bi-eye-fill" id="ipPasswordToggleIcon<?php echo $ip['id']; ?>"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <span class="text-muted">لا توجد عناوين IP</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>اسم المستخدم</th>
                            <td><?php echo htmlspecialchars($agent['username'] ?? '-'); ?></td>
                        </tr>
                        <tr>
                            <th>كلمة المرور</th>
                            <td>
                                <div class="input-group">
                                    <input type="password" class="form-control" 
                                           id="password" value="<?php echo htmlspecialchars($agent['password'] ?? ''); ?>" readonly>
                                    <button class="btn btn-outline-secondary" type="button" 
                                            onclick="togglePassword()">
                                        <i class="bi bi-eye-fill"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th>RP-SUB</th>
                            <td><?php echo htmlspecialchars($agent['rp_sub'] ?? '-'); ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card animate__animated animate__fadeIn" style="animation-delay: 0.2s">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="bi bi-hdd-network-fill me-2"></i>
                        معلومات الشبكة
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-custom table-striped">
                        <tr>
                            <th width="30%">المنفذ</th>
                            <td><?php echo htmlspecialchars($agent['port'] ?? '-'); ?></td>
                        </tr>
                        <tr>
                            <th>MAC Address</th>
                            <td class="number"><?php echo htmlspecialchars($agent['mac_address'] ?? '-'); ?></td>
                        </tr>
                        <tr>
                            <th>SSID</th>
                            <td><?php echo htmlspecialchars($agent['ssid'] ?? '-'); ?></td>
                        </tr>
                        <tr>
                            <th>ملاحظات على المشترك</th>
                            <td><?php echo nl2br(htmlspecialchars($agent['notes'] ?? '-')); ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>


</div>



<!-- دالة لإظهار/إخفاء كلمة المرور -->
<script>
// دالة لإظهار/إخفاء كلمة المرور
function togglePassword() {
    const passwordField = document.getElementById('password');
    const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
    passwordField.setAttribute('type', type);
    
    // تغيير أيقونة الزر
    const icon = document.querySelector('button[onclick="togglePassword()"] i');
    if (type === 'text') {
        icon.classList.remove('bi-eye-fill');
        icon.classList.add('bi-eye-slash-fill');
    } else {
        icon.classList.remove('bi-eye-slash-fill');
        icon.classList.add('bi-eye-fill');
    }
}

// دالة لإظهار/إخفاء كلمة المرور لعناوين IP
function toggleIpPassword(id) {
    const passwordField = document.getElementById(`ipPassword${id}`);
    const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
    passwordField.setAttribute('type', type);
    
    // تغيير أيقونة الزر
    const icon = document.getElementById(`ipPasswordToggleIcon${id}`);
    if (type === 'text') {
        icon.classList.remove('bi-eye-fill');
        icon.classList.add('bi-eye-slash-fill');
    } else {
        icon.classList.remove('bi-eye-slash-fill');
        icon.classList.add('bi-eye-fill');
    }
}
</script>



<?php include '../includes/footer.php'; ?>
