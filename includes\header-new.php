<?php
require_once dirname(__DIR__) . '/includes/init.php';
check_login();
check_page_permissions();
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المشتركين - التصميم الجديد</title>
    
    <!-- الخطوط -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    
    <!-- Bootstrap و الأيقونات -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" rel="stylesheet">
    
    <!-- الرسوم المتحركة -->
    <link href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css" rel="stylesheet">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <!-- التصميم الجديد -->
    <link href="<?php echo get_path('/assets/css/new-design.css'); ?>" rel="stylesheet">
    
    <style>
        /* تحسينات إضافية */
        .notification-toast {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            backdrop-filter: blur(10px);
            border: none;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.2);
        }
        
        .page-header {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
        }
        
        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            color: #6c757d;
            font-size: 1.1rem;
            font-weight: 500;
        }
        
        .sidebar-modern {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .quick-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-bottom: 2rem;
        }
        
        .quick-action-btn {
            flex: 1;
            min-width: 200px;
            padding: 1rem;
            background: rgba(255,255,255,0.9);
            border: none;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .quick-action-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
            color: inherit;
            text-decoration: none;
        }
        
        .quick-action-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }
    </style>
</head>
<body class="fade-in">
    <!-- إشعارات النظام -->
    <?php if (isset($_SESSION['error'])): ?>
        <div class="toast notification-toast show" role="alert" data-bs-autohide="true" data-bs-delay="5000">
            <div class="toast-header bg-danger text-white">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                <strong class="me-auto">خطأ</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                <?php 
                echo $_SESSION['error'];
                unset($_SESSION['error']);
                ?>
            </div>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="toast notification-toast show" role="alert" data-bs-autohide="true" data-bs-delay="5000">
            <div class="toast-header bg-success text-white">
                <i class="bi bi-check-circle-fill me-2"></i>
                <strong class="me-auto">نجح</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                <?php 
                echo $_SESSION['success'];
                unset($_SESSION['success']);
                ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- الشريط العلوي الجديد -->
    <nav class="navbar navbar-expand-lg modern-navbar">
        <div class="container-fluid">
            <a class="navbar-brand floating" href="<?php echo get_path('/pages/dashboard.php'); ?>">
                <i class="fas fa-network-wired"></i>
                نظام إدارة المشتركين
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <!-- لوحة التحكم -->
                    <li class="nav-item">
                        <a class="nav-link <?php echo is_active_page('dashboard.php'); ?>" 
                           href="<?php echo get_path('/pages/dashboard.php'); ?>">
                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        </a>
                    </li>

                    <!-- إدارة الوكلاء -->
                    <?php if (currentUserHasPermission('agents.view')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo is_active_page('agents.php'); ?>" 
                           href="<?php echo get_path('/pages/agents.php'); ?>">
                            <i class="fas fa-users me-2"></i>إدارة المشتركين
                        </a>
                    </li>
                    <?php endif; ?>

                    <!-- تصدير البيانات -->
                    <?php if (currentUserHasPermission('reports.export')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo is_active_page('export.php'); ?>" 
                           href="<?php echo get_path('/pages/export.php'); ?>">
                            <i class="fas fa-file-export me-2"></i>تصدير البيانات
                        </a>
                    </li>
                    <?php endif; ?>

                    <!-- إدارة النظام -->
                    <?php if (currentUserHasPermission('system.admin_access')): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" 
                           data-bs-toggle="dropdown">
                            <i class="fas fa-cogs me-2"></i>إدارة النظام
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="<?php echo get_path('/pages/manage_users.php'); ?>">
                                <i class="fas fa-user-cog me-2"></i>إدارة المستخدمين
                            </a></li>
                            <li><a class="dropdown-item" href="<?php echo get_path('/pages/permissions.php'); ?>">
                                <i class="fas fa-shield-alt me-2"></i>إدارة الصلاحيات
                            </a></li>
                            <li><a class="dropdown-item" href="<?php echo get_path('/pages/backup.php'); ?>">
                                <i class="fas fa-database me-2"></i>النسخ الاحتياطي
                            </a></li>
                            <li><a class="dropdown-item" href="<?php echo get_path('/pages/settings.php'); ?>">
                                <i class="fas fa-sliders-h me-2"></i>إعدادات النظام
                            </a></li>
                        </ul>
                    </li>
                    <?php endif; ?>
                </ul>

                <!-- قائمة المستخدم -->
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" 
                           data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-2"></i>
                            <?php echo htmlspecialchars($_SESSION['user_name']); ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="<?php echo get_path('/pages/profile.php'); ?>">
                                <i class="fas fa-user me-2"></i>الملف الشخصي
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="<?php echo get_path('/logout.php'); ?>">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <!-- هنا سيتم إدراج محتوى الصفحة -->
