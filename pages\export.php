<?php
require_once '../includes/init.php';
require_once '../includes/export.class.php';

// التحقق من تسجيل الدخول
check_login();

// التحقق من صلاحية تصدير البيانات
requirePermission('reports.export');

// التأكد من وجود مجلد التصدير
$export_dir = dirname(__DIR__) . '/exports';
if (!is_dir($export_dir)) {
    mkdir($export_dir, 0755, true);
}

// معالجة طلب التصدير
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $export = new ExportManager($conn);
        
        // جمع الفلاتر
        $filters = [
            'status' => $_POST['status'] ?? null,
            'device_type' => $_POST['device_type'] ?? null,
            'repeater' => $_POST['repeater'] ?? null
        ];

        // تصدير البيانات
        $filename = $export->exportToCSV($filters);
        
        // تحميل الملف
        $file = $export_dir . '/' . $filename;
        if (file_exists($file)) {
            header('Content-Type: text/csv; charset=utf-8');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Content-Length: ' . filesize($file));
            header('Cache-Control: max-age=0');
            readfile($file);
            
            // حذف الملف بعد التحميل
            unlink($file);
            exit;
        }
    } catch (Exception $e) {
        $error = "حدث خطأ أثناء التصدير: " . $e->getMessage();
    }
}

// جلب قائمة أنواع الأجهزة والريبيترات للفلتر
$device_types = $conn->query("SELECT id, name FROM device_types ORDER BY name")->fetch_all(MYSQLI_ASSOC);
$repeaters = $conn->query("SELECT id, name FROM repeaters ORDER BY name")->fetch_all(MYSQLI_ASSOC);

require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6>تصدير بيانات المشتركين</h6>
                </div>
                <div class="card-body">
                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php endif; ?>

                    <form method="POST" class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">الحالة</label>
                            <select name="status" class="form-select">
                                <option value="">الكل</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>

                        <div class="col-md-4">
                            <label class="form-label">نوع الجهاز</label>
                            <select name="device_type" class="form-select">
                                <option value="">الكل</option>
                                <?php foreach ($device_types as $type): ?>
                                    <option value="<?php echo $type['id']; ?>">
                                        <?php echo htmlspecialchars($type['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-md-4">
                            <label class="form-label">الريبيتر</label>
                            <select name="repeater" class="form-select">
                                <option value="">الكل</option>
                                <?php foreach ($repeaters as $repeater): ?>
                                    <option value="<?php echo $repeater['id']; ?>">
                                        <?php echo htmlspecialchars($repeater['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-download me-1"></i>
                                تصدير البيانات (CSV)
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
