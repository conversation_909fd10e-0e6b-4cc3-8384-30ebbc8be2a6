<?php
require_once '../includes/init.php';

// التحقق من تسجيل الدخول
check_login();

// التحقق من صلاحية تصدير البيانات
requirePermission('reports.export');

$page_title = "تصدير بيانات المشتركين";
include '../includes/header.php';

// إضافة التصميم الجديد
echo "<style>
/* خلفية ثابتة بالألوان الجديدة لصفحة التصدير */
body {
    background: linear-gradient(135deg, #38C5E6 0%, #0B8582 50%, #01233D 100%);
    background-attachment: fixed;
    min-height: 100vh;
}

.container-fluid {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    margin: 1rem;
}

.card {
    transition: all 0.3s ease !important;
    border: none !important;
    border-radius: 20px !important;
    backdrop-filter: blur(10px);
    background: rgba(255,255,255,0.95) !important;
}

.card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 15px 35px rgba(0,0,0,0.15) !important;
}

.btn {
    border-radius: 15px !important;
    padding: 0.75rem 1.5rem !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    border: none !important;
}

.btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.btn-primary { background: linear-gradient(135deg, #38C5E6 0%, #0B8582 100%) !important; }
.btn-success { background: linear-gradient(135deg, #0B8582 0%, #01233D 100%) !important; }
.btn-warning { background: linear-gradient(135deg, #38C5E6 0%, #01233D 100%) !important; }
.btn-danger { background: linear-gradient(135deg, #01233D 0%, #0B8582 100%) !important; }
.btn-info { background: linear-gradient(135deg, #38C5E6 0%, #0B8582 100%) !important; }

.form-control, .form-select {
    border-radius: 15px !important;
    border: 2px solid rgba(56, 197, 230, 0.2) !important;
    padding: 0.75rem 1rem !important;
    transition: all 0.3s ease !important;
}

.form-control:focus, .form-select:focus {
    border-color: #38C5E6 !important;
    box-shadow: 0 0 0 0.2rem rgba(56, 197, 230, 0.25) !important;
    transform: translateY(-2px) !important;
}

.table {
    background: rgba(255,255,255,0.95);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.table thead th {
    background: linear-gradient(135deg, #01233D 0%, #0B8582 100%);
    color: white;
    border: none;
    padding: 1.2rem;
    font-weight: 600;
}

.table tbody td {
    border: none;
    padding: 1rem;
    vertical-align: middle;
}

.table tbody tr:hover {
    background: linear-gradient(135deg, rgba(56, 197, 230, 0.1) 0%, rgba(11, 133, 130, 0.1) 100%);
    transform: translateX(5px);
}

.export-card {
    background: rgba(255,255,255,0.95);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.export-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #38C5E6 0%, #0B8582 100%);
}

.export-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.export-type-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin-bottom: 1rem;
}

.animate__fadeInUp {
    animation-duration: 0.8s;
}

/* تحسين وضوح النصوص */
.form-label {
    color: #01233D !important;
    font-weight: 700 !important;
    font-size: 1.1rem !important;
    text-shadow: 1px 1px 2px rgba(255,255,255,0.9) !important;
    background: rgba(255,255,255,0.95) !important;
    padding: 0.4rem 0.8rem !important;
    border-radius: 8px !important;
    display: inline-block !important;
    border: 2px solid rgba(56, 197, 230, 0.3) !important;
    margin-bottom: 0.8rem !important;
}

.card-body {
    background: rgba(255,255,255,0.98) !important;
    color: #01233D !important;
}

.form-select {
    color: #01233D !important;
    font-weight: 600 !important;
}

.form-select option {
    color: #01233D !important;
    background: #ffffff !important;
}
</style>";

// معالجة طلب التصدير
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $export_type = $_POST['export_type'] ?? '';
        $status_filter = $_POST['status'] ?? '';
        
        // التأكد من وجود مجلد التصدير
        $export_dir = dirname(__DIR__) . '/exports';
        if (!is_dir($export_dir)) {
            mkdir($export_dir, 0755, true);
        }
        
        $filename = 'agents_export_' . date('Y-m-d_H-i-s') . '.csv';
        $filepath = $export_dir . '/' . $filename;
        
        // إنشاء ملف CSV
        $fp = fopen($filepath, 'w');
        if ($fp === false) {
            throw new Exception("Could not create export file");
        }
        
        // Add BOM for Excel Arabic support
        fprintf($fp, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // تحديد الحقول حسب نوع التصدير
        $columns = [];
        $sql = "";
        
        switch ($export_type) {
            case 'basic':
                $columns = [
                    'agent_name' => 'اسم المشترك',
                    'phone_number' => 'رقم الهاتف',
                    'point_name' => 'العنوان',
                    'unms_status' => 'الحالة',
                    'created_at' => 'تاريخ الإنشاء'
                ];
                $sql = "SELECT agent_name, phone_number, point_name, unms_status, created_at FROM agents WHERE 1=1";
                break;
                
            case 'detailed':
                $columns = [
                    'agent_name' => 'اسم المشترك',
                    'phone_number' => 'رقم الهاتف',
                    'secondary_phone_number' => 'رقم الهاتف الثانوي',
                    'point_name' => 'العنوان',
                    'unms_status' => 'الحالة',
                    'username' => 'اسم المستخدم',
                    'rp_sub' => 'RP-SUB',
                    'mac_address' => 'MAC Address',
                    'port' => 'المنفذ',
                    'ssid' => 'SSID',
                    'device_ownership' => 'ملكية الجهاز',
                    'serial_number' => 'تسلسل',
                    'sn_onu' => 'SN ONU',
                    'sector_number' => 'رقم السكتر',
                    'bill_price' => 'سعر فواتير',
                    'notes' => 'ملاحظات',
                    'created_at' => 'تاريخ الإنشاء'
                ];
                $sql = "SELECT agent_name, phone_number, secondary_phone_number, point_name, unms_status, username, rp_sub, mac_address, port, ssid, device_ownership, serial_number, sn_onu, sector_number, bill_price, notes, created_at FROM agents WHERE 1=1";
                break;
                
            case 'financial':
                $columns = [
                    'agent_name' => 'اسم المشترك',
                    'phone_number' => 'رقم الهاتف',
                    'bill_price' => 'سعر فواتير',
                    'unms_status' => 'الحالة',
                    'created_at' => 'تاريخ الإنشاء'
                ];
                $sql = "SELECT agent_name, phone_number, bill_price, unms_status, created_at FROM agents WHERE 1=1";
                break;
                
            default:
                throw new Exception("نوع التصدير غير صحيح");
        }
        
        // إضافة فلتر الحالة
        if (!empty($status_filter)) {
            $sql .= " AND unms_status = '" . $conn->real_escape_string($status_filter) . "'";
        }
        
        $sql .= " ORDER BY created_at DESC";
        
        // كتابة رؤوس الأعمدة
        fputcsv($fp, array_values($columns));
        
        // تنفيذ الاستعلام وكتابة البيانات
        $result = $conn->query($sql);
        if (!$result) {
            throw new Exception("Error executing query: " . $conn->error);
        }
        
        $count = 0;
        while ($row = $result->fetch_assoc()) {
            $exportRow = [];
            foreach ($columns as $field => $title) {
                $value = $row[$field] ?? '';
                
                // تنسيق البيانات
                if ($field === 'created_at' && $value) {
                    $value = date('Y-m-d H:i', strtotime($value));
                }
                
                if ($field === 'unms_status') {
                    $value = $value === 'active' ? 'نشط' : 'غير نشط';
                }
                
                if ($field === 'bill_price' && $value) {
                    $value = number_format($value, 2) . ' دينار';
                }
                
                $exportRow[] = $value;
            }
            fputcsv($fp, $exportRow);
            $count++;
        }
        
        fclose($fp);
        
        if ($count > 0) {
            // تحميل الملف
            header('Content-Type: text/csv; charset=utf-8');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Content-Length: ' . filesize($filepath));
            header('Cache-Control: max-age=0');
            readfile($filepath);
            
            // حذف الملف بعد التحميل
            unlink($filepath);
            exit;
        } else {
            $error = "لا توجد بيانات للتصدير مع الفلاتر المحددة.";
        }
        
    } catch (Exception $e) {
        $error = "حدث خطأ أثناء التصدير: " . $e->getMessage();
        error_log("Export error: " . $e->getMessage());
    }
}

// جلب الإحصائيات
$total_agents = 0;
$active_agents = 0;
$inactive_agents = 0;

try {
    $result = $conn->query("SELECT COUNT(*) as count FROM agents");
    if ($result) {
        $total_agents = $result->fetch_assoc()['count'] ?? 0;
    }
    
    $result = $conn->query("SELECT COUNT(*) as count FROM agents WHERE unms_status = 'active'");
    if ($result) {
        $active_agents = $result->fetch_assoc()['count'] ?? 0;
    }
    
    $inactive_agents = $total_agents - $active_agents;
} catch (Exception $e) {
    error_log("Error fetching statistics: " . $e->getMessage());
}
?>

<div class="container-fluid py-4">
    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-lg animate__animated animate__fadeInDown"
                 style="background: #01233D !important; border-radius: 20px;">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="text-white">
                            <h1 class="h2 mb-2 fw-bold" style="color: #ffffff !important; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
                                <i class="fas fa-file-export me-3" style="font-size: 2.5rem; color: #38C5E6;"></i>
                                تصدير بيانات المشتركين
                            </h1>
                            <p class="mb-0" style="font-size: 1.1rem; color: #ffffff !important; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">
                                <i class="fas fa-download me-2" style="color: #38C5E6;"></i>
                                تصدير وحفظ بيانات المشتركين بصيغة CSV مع أنواع مختلفة من التصدير
                            </p>
                        </div>
                        <div class="text-white text-center">
                            <div class="rounded-circle p-3 mb-2" style="width: 80px; height: 80px; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, #38C5E6 0%, #0B8582 100%);">
                                <i class="fas fa-chart-bar" style="font-size: 2rem; color: #ffffff;"></i>
                            </div>
                            <small style="color: #ffffff;">تقارير شاملة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-4 mb-3">
            <div class="export-card animate__animated animate__fadeInUp" style="animation-delay: 0.1s;">
                <div class="d-flex align-items-center">
                    <div class="export-type-icon" style="background: linear-gradient(135deg, #38C5E6 0%, #0B8582 100%);">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="ms-3">
                        <h5 class="mb-1 fw-bold"><?php echo number_format($total_agents); ?></h5>
                        <small class="text-muted">إجمالي المشتركين</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="export-card animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
                <div class="d-flex align-items-center">
                    <div class="export-type-icon" style="background: linear-gradient(135deg, #0B8582 0%, #01233D 100%);">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="ms-3">
                        <h5 class="mb-1 fw-bold"><?php echo number_format($active_agents); ?></h5>
                        <small class="text-muted">المشتركين النشطين</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="export-card animate__animated animate__fadeInUp" style="animation-delay: 0.3s;">
                <div class="d-flex align-items-center">
                    <div class="export-type-icon" style="background: linear-gradient(135deg, #38C5E6 0%, #01233D 100%);">
                        <i class="fas fa-pause-circle"></i>
                    </div>
                    <div class="ms-3">
                        <h5 class="mb-1 fw-bold"><?php echo number_format($inactive_agents); ?></h5>
                        <small class="text-muted">غير النشطين</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسائل -->
    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeInUp" role="alert" style="border-radius: 15px;">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeInUp" role="alert" style="border-radius: 15px;">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- أنواع التصدير -->
    <div class="row">
        <div class="col-12">
            <div class="card animate__animated animate__fadeInUp" style="animation-delay: 0.4s;">
                <div class="card-header border-0 pb-0"
                     style="background: linear-gradient(135deg, #01233D 0%, #0B8582 100%); border-radius: 20px 20px 0 0;">
                    <div class="d-flex align-items-center p-3">
                        <i class="fas fa-download text-white me-3" style="font-size: 1.5rem;"></i>
                        <h5 class="mb-0 text-white fw-bold">أنواع التصدير المتاحة</h5>
                    </div>
                </div>
                <div class="card-body p-4">
                    <form method="POST" class="row g-4">
                        <!-- نوع التصدير -->
                        <div class="col-12">
                            <label class="form-label fw-bold">
                                <i class="fas fa-list me-2 text-primary"></i>
                                اختر نوع التصدير
                            </label>
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <div class="card h-100" style="cursor: pointer; transition: all 0.3s ease;" onclick="selectExportType('basic')">
                                        <div class="card-body text-center">
                                            <div class="export-type-icon mx-auto mb-3" style="background: linear-gradient(135deg, #38C5E6 0%, #0B8582 100%); width: 60px; height: 60px; font-size: 1.5rem;">
                                                <i class="fas fa-file-alt"></i>
                                            </div>
                                            <h6 class="fw-bold">تصدير أساسي</h6>
                                            <small class="text-muted">المعلومات الأساسية فقط</small>
                                            <div class="form-check mt-2">
                                                <input class="form-check-input" type="radio" name="export_type" value="basic" id="basic">
                                                <label class="form-check-label" for="basic">اختيار</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card h-100" style="cursor: pointer; transition: all 0.3s ease;" onclick="selectExportType('detailed')">
                                        <div class="card-body text-center">
                                            <div class="export-type-icon mx-auto mb-3" style="background: linear-gradient(135deg, #0B8582 0%, #01233D 100%); width: 60px; height: 60px; font-size: 1.5rem;">
                                                <i class="fas fa-file-invoice"></i>
                                            </div>
                                            <h6 class="fw-bold">تصدير مفصل</h6>
                                            <small class="text-muted">جميع المعلومات التقنية</small>
                                            <div class="form-check mt-2">
                                                <input class="form-check-input" type="radio" name="export_type" value="detailed" id="detailed">
                                                <label class="form-check-label" for="detailed">اختيار</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card h-100" style="cursor: pointer; transition: all 0.3s ease;" onclick="selectExportType('financial')">
                                        <div class="card-body text-center">
                                            <div class="export-type-icon mx-auto mb-3" style="background: linear-gradient(135deg, #38C5E6 0%, #01233D 100%); width: 60px; height: 60px; font-size: 1.5rem;">
                                                <i class="fas fa-dollar-sign"></i>
                                            </div>
                                            <h6 class="fw-bold">تصدير مالي</h6>
                                            <small class="text-muted">المعلومات المالية فقط</small>
                                            <div class="form-check mt-2">
                                                <input class="form-check-input" type="radio" name="export_type" value="financial" id="financial">
                                                <label class="form-check-label" for="financial">اختيار</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- فلتر الحالة -->
                        <div class="col-md-6">
                            <label class="form-label fw-bold">
                                <i class="fas fa-filter me-2 text-success"></i>
                                فلتر الحالة (اختياري)
                            </label>
                            <select name="status" class="form-select">
                                <option value="">جميع الحالات</option>
                                <option value="active">النشطين فقط</option>
                                <option value="inactive">غير النشطين فقط</option>
                            </select>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="col-md-6">
                            <div class="alert alert-info mb-0" style="border-radius: 15px;">
                                <h6><i class="fas fa-info-circle me-2"></i>معلومات التصدير:</h6>
                                <ul class="mb-0 small">
                                    <li><strong>أساسي:</strong> الاسم، الهاتف، العنوان، الحالة</li>
                                    <li><strong>مفصل:</strong> جميع المعلومات التقنية</li>
                                    <li><strong>مالي:</strong> الاسم، الهاتف، السعر، الحالة</li>
                                </ul>
                            </div>
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="col-12">
                            <div class="d-flex gap-3 justify-content-center">
                                <button type="submit" class="btn btn-primary btn-lg px-5">
                                    <i class="fas fa-download me-2"></i>
                                    تصدير البيانات (CSV)
                                </button>
                                <button type="reset" class="btn btn-secondary btn-lg px-4">
                                    <i class="fas fa-undo me-2"></i>
                                    إعادة تعيين
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <?php if ($total_agents == 0): ?>
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-warning" style="border-radius: 15px;">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>تنبيه</h5>
                    <p class="mb-3">لا توجد بيانات مشتركين في قاعدة البيانات للتصدير.</p>
                    <a href="add_agent.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إضافة مشترك جديد
                    </a>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
function selectExportType(type) {
    // إزالة التحديد من جميع البطاقات
    document.querySelectorAll('.card').forEach(card => {
        card.style.border = '';
        card.style.boxShadow = '';
    });

    // تحديد البطاقة المختارة
    const selectedCard = document.querySelector(`#${type}`).closest('.card');
    selectedCard.style.border = '3px solid #38C5E6';
    selectedCard.style.boxShadow = '0 8px 25px rgba(56, 197, 230, 0.3)';

    // تحديد الراديو بتن
    document.getElementById(type).checked = true;
}

// تحديد النوع الأول افتراضياً
document.addEventListener('DOMContentLoaded', function() {
    selectExportType('basic');
});
</script>

<?php include '../includes/footer.php'; ?>
