<?php
require_once '../includes/init.php';
require_once '../includes/export.class.php';

// التحقق من تسجيل الدخول
check_login();

// التحقق من صلاحية تصدير البيانات
requirePermission('reports.export');

// إضافة التصميم الجديد
echo "<style>
/* تأثيرات إضافية لصفحة التصدير */
body {
    background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
    background-size: 400% 400%;
    animation: gradientBG 15s ease infinite;
}

@keyframes gradientBG {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.container-fluid {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    margin: 1rem;
}

.card {
    transition: all 0.3s ease !important;
    border: none !important;
    border-radius: 20px !important;
    backdrop-filter: blur(10px);
    background: rgba(255,255,255,0.95) !important;
}

.card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 15px 35px rgba(0,0,0,0.15) !important;
}

.btn {
    border-radius: 15px !important;
    padding: 0.75rem 1.5rem !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    border: none !important;
}

.btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.btn-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; }
.btn-success { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%) !important; }
.btn-warning { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%) !important; }
.btn-danger { background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%) !important; }
.btn-info { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important; }

.form-control, .form-select {
    border-radius: 15px !important;
    border: 2px solid rgba(102, 126, 234, 0.2) !important;
    padding: 0.75rem 1rem !important;
    transition: all 0.3s ease !important;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
    transform: translateY(-2px) !important;
}

.alert {
    border-radius: 15px !important;
    border: none !important;
    backdrop-filter: blur(10px);
}

.export-stats {
    background: rgba(255,255,255,0.95);
    border-radius: 20px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.export-stats::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.export-stats:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.animate__fadeInUp {
    animation-duration: 0.8s;
}

.progress {
    height: 8px;
    border-radius: 10px;
    background: rgba(102, 126, 234, 0.1);
}

.progress-bar {
    border-radius: 10px;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}
</style>";

// التأكد من وجود مجلد التصدير
$export_dir = dirname(__DIR__) . '/exports';
if (!is_dir($export_dir)) {
    mkdir($export_dir, 0755, true);
}

// معالجة طلب التصدير
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $export = new ExportManager($conn);
        
        // جمع الفلاتر
        $filters = [
            'status' => $_POST['status'] ?? null,
            'device_type' => $_POST['device_type'] ?? null,
            'repeater' => $_POST['repeater'] ?? null
        ];

        // تصدير البيانات
        $filename = $export->exportToCSV($filters);
        
        // تحميل الملف
        $file = $export_dir . '/' . $filename;
        if (file_exists($file)) {
            header('Content-Type: text/csv; charset=utf-8');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Content-Length: ' . filesize($file));
            header('Cache-Control: max-age=0');
            readfile($file);
            
            // حذف الملف بعد التحميل
            unlink($file);
            exit;
        }
    } catch (Exception $e) {
        $error = "حدث خطأ أثناء التصدير: " . $e->getMessage();
    }
}

// جلب قائمة أنواع الأجهزة والريبيترات للفلتر
$device_types = $conn->query("SELECT id, name FROM device_types ORDER BY name")->fetch_all(MYSQLI_ASSOC);
$repeaters = $conn->query("SELECT id, name FROM repeaters ORDER BY name")->fetch_all(MYSQLI_ASSOC);

require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <!-- رأس الصفحة المحدث -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card header-card border-0 shadow-lg animate__animated animate__fadeInDown"
                 style="background: linear-gradient(135deg, #1a1a1a 0%, #2c2c2c 100%) !important; border-radius: 20px;">
                <div class="card-body p-4" style="background: transparent !important;">
                    <div class="d-flex justify-content-between align-items-center">
                        <div style="background: transparent !important;">
                            <h1 class="h2 mb-2 fw-bold" style="color: #ffffff !important; background: transparent !important;">
                                <i class="fas fa-file-export me-3" style="font-size: 2.5rem; color: #4facfe;"></i>
                                تصدير بيانات المشتركين
                            </h1>
                            <p class="mb-0" style="font-size: 1.1rem; color: #ffffff !important; background: transparent !important;">
                                <i class="fas fa-download me-2" style="color: #4facfe;"></i>
                                تصدير وحفظ بيانات المشتركين بصيغة CSV مع فلاتر متقدمة
                            </p>
                        </div>
                        <div class="text-white text-center">
                            <div class="rounded-circle p-3 mb-2" style="width: 80px; height: 80px; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                                <i class="fas fa-chart-bar" style="font-size: 2rem; color: #ffffff;"></i>
                            </div>
                            <small style="color: #ffffff;">تقارير شاملة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="export-stats animate__animated animate__fadeInUp" style="animation-delay: 0.1s;">
                <div class="d-flex align-items-center">
                    <div class="rounded-circle bg-primary bg-opacity-10 p-3 me-3">
                        <i class="fas fa-users text-primary" style="font-size: 1.5rem;"></i>
                    </div>
                    <div>
                        <h5 class="mb-1 fw-bold"><?php echo number_format($total_agents ?? 0); ?></h5>
                        <small class="text-muted">إجمالي المشتركين</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="export-stats animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
                <div class="d-flex align-items-center">
                    <div class="rounded-circle bg-success bg-opacity-10 p-3 me-3">
                        <i class="fas fa-check-circle text-success" style="font-size: 1.5rem;"></i>
                    </div>
                    <div>
                        <h5 class="mb-1 fw-bold"><?php echo number_format($active_agents ?? 0); ?></h5>
                        <small class="text-muted">المشتركين النشطين</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="export-stats animate__animated animate__fadeInUp" style="animation-delay: 0.3s;">
                <div class="d-flex align-items-center">
                    <div class="rounded-circle bg-warning bg-opacity-10 p-3 me-3">
                        <i class="fas fa-pause-circle text-warning" style="font-size: 1.5rem;"></i>
                    </div>
                    <div>
                        <h5 class="mb-1 fw-bold"><?php echo number_format($inactive_agents ?? 0); ?></h5>
                        <small class="text-muted">غير النشطين</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="export-stats animate__animated animate__fadeInUp" style="animation-delay: 0.4s;">
                <div class="d-flex align-items-center">
                    <div class="rounded-circle bg-info bg-opacity-10 p-3 me-3">
                        <i class="fas fa-file-csv text-info" style="font-size: 1.5rem;"></i>
                    </div>
                    <div>
                        <h5 class="mb-1 fw-bold">CSV</h5>
                        <small class="text-muted">صيغة التصدير</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسائل -->
    <?php if (isset($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeInUp" role="alert" style="border-radius: 15px;">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- نموذج التصدير -->
    <div class="row">
        <div class="col-12">
            <div class="card animate__animated animate__fadeInUp" style="animation-delay: 0.5s;">
                <div class="card-header border-0 pb-0"
                     style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 20px 20px 0 0;">
                    <div class="d-flex align-items-center p-3">
                        <i class="fas fa-filter text-white me-3" style="font-size: 1.5rem;"></i>
                        <h5 class="mb-0 text-white fw-bold">فلاتر التصدير</h5>
                    </div>
                </div>
                <div class="card-body p-4">

                    <form method="POST" class="row g-4">
                        <div class="col-md-4">
                            <label class="form-label fw-bold">
                                <i class="fas fa-toggle-on me-2 text-primary"></i>
                                الحالة
                            </label>
                            <select name="status" class="form-select">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>

                        <div class="col-md-4">
                            <label class="form-label fw-bold">
                                <i class="fas fa-laptop me-2 text-success"></i>
                                نوع الجهاز
                            </label>
                            <select name="device_type" class="form-select">
                                <option value="">جميع الأجهزة</option>
                                <?php foreach ($device_types as $type): ?>
                                    <option value="<?php echo $type['id']; ?>">
                                        <?php echo htmlspecialchars($type['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-md-4">
                            <label class="form-label fw-bold">
                                <i class="fas fa-broadcast-tower me-2 text-warning"></i>
                                الريبيتر
                            </label>
                            <select name="repeater" class="form-select">
                                <option value="">جميع الريبيترات</option>
                                <?php foreach ($repeaters as $repeater): ?>
                                    <option value="<?php echo $repeater['id']; ?>">
                                        <?php echo htmlspecialchars($repeater['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-12">
                            <div class="d-flex gap-3 justify-content-center">
                                <button type="submit" class="btn btn-primary btn-lg px-5">
                                    <i class="fas fa-download me-2"></i>
                                    تصدير البيانات (CSV)
                                </button>
                                <button type="reset" class="btn btn-secondary btn-lg px-4">
                                    <i class="fas fa-undo me-2"></i>
                                    إعادة تعيين
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
