<?php
require_once '../includes/init.php';
require_once '../includes/export.class.php';

// التحقق من تسجيل الدخول
check_login();

// التحقق من صلاحية تصدير البيانات
requirePermission('reports.export');

// إضافة التصميم الجديد
echo "<style>
/* خلفية ثابتة بالألوان الجديدة لصفحة التصدير */
body {
    background: linear-gradient(135deg, #38C5E6 0%, #0B8582 50%, #01233D 100%);
    background-attachment: fixed;
    min-height: 100vh;
}

.container-fluid {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    margin: 1rem;
}

.card {
    transition: all 0.3s ease !important;
    border: none !important;
    border-radius: 20px !important;
    backdrop-filter: blur(10px);
    background: rgba(255,255,255,0.95) !important;
}

.card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 15px 35px rgba(0,0,0,0.15) !important;
}

.btn {
    border-radius: 15px !important;
    padding: 0.75rem 1.5rem !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    border: none !important;
}

.btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.btn-primary { background: linear-gradient(135deg, #38C5E6 0%, #0B8582 100%) !important; }
.btn-success { background: linear-gradient(135deg, #0B8582 0%, #01233D 100%) !important; }
.btn-warning { background: linear-gradient(135deg, #38C5E6 0%, #01233D 100%) !important; }
.btn-danger { background: linear-gradient(135deg, #01233D 0%, #0B8582 100%) !important; }
.btn-info { background: linear-gradient(135deg, #38C5E6 0%, #0B8582 100%) !important; }

.form-control, .form-select {
    border-radius: 15px !important;
    border: 2px solid rgba(56, 197, 230, 0.2) !important;
    padding: 0.75rem 1rem !important;
    transition: all 0.3s ease !important;
}

.form-control:focus, .form-select:focus {
    border-color: #38C5E6 !important;
    box-shadow: 0 0 0 0.2rem rgba(56, 197, 230, 0.25) !important;
    transform: translateY(-2px) !important;
}

.alert {
    border-radius: 15px !important;
    border: none !important;
    backdrop-filter: blur(10px);
}

.export-stats {
    background: rgba(255,255,255,0.95);
    border-radius: 20px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.export-stats::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #38C5E6 0%, #0B8582 100%);
}

.export-stats:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.animate__fadeInUp {
    animation-duration: 0.8s;
}

.progress {
    height: 8px;
    border-radius: 10px;
    background: rgba(102, 126, 234, 0.1);
}

.progress-bar {
    border-radius: 10px;
    background: linear-gradient(135deg, #38C5E6 0%, #0B8582 100%);
}

/* تحسين وضوح النصوص في نموذج التصدير */
.card-body .form-label {
    color: #01233D !important;
    font-weight: 700 !important;
    font-size: 1.1rem !important;
    text-shadow: 1px 1px 2px rgba(255,255,255,0.9) !important;
    background: rgba(255,255,255,0.95) !important;
    padding: 0.4rem 0.8rem !important;
    border-radius: 8px !important;
    display: inline-block !important;
    border: 2px solid rgba(56, 197, 230, 0.3) !important;
    margin-bottom: 0.8rem !important;
}

.card-body {
    background: rgba(255,255,255,0.98) !important;
    color: #01233D !important;
}

.form-select {
    color: #01233D !important;
    font-weight: 600 !important;
}

.form-select option {
    color: #01233D !important;
    background: #ffffff !important;
}
</style>";

// التأكد من وجود مجلد التصدير
$export_dir = dirname(__DIR__) . '/exports';
if (!is_dir($export_dir)) {
    mkdir($export_dir, 0755, true);
}

// معالجة طلب التصدير
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $export = new ExportManager($conn);
        
        // جمع الفلاتر
        $filters = [
            'status' => $_POST['status'] ?? null,
            'service_type' => $_POST['service_type'] ?? null,
            'branch_name' => $_POST['branch_name'] ?? null,
            'tower_location' => $_POST['tower_location'] ?? null,
            'cabinet_location' => $_POST['cabinet_location'] ?? null
        ];

        // تصدير البيانات
        $filename = $export->exportToCSV($filters);
        
        // تحميل الملف
        $file = $export_dir . '/' . $filename;
        if (file_exists($file)) {
            header('Content-Type: text/csv; charset=utf-8');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Content-Length: ' . filesize($file));
            header('Cache-Control: max-age=0');
            readfile($file);
            
            // حذف الملف بعد التحميل
            unlink($file);
            exit;
        }
    } catch (Exception $e) {
        $error = "حدث خطأ أثناء التصدير: " . $e->getMessage();
    }
}

// جلب قوائم البيانات للفلاتر
$service_types = $conn->query("SELECT id, name FROM service_types WHERE status = 'active' ORDER BY name")->fetch_all(MYSQLI_ASSOC);
$branch_names = $conn->query("SELECT id, name FROM branch_names WHERE status = 'active' ORDER BY name")->fetch_all(MYSQLI_ASSOC);
$tower_locations = $conn->query("SELECT id, name FROM tower_locations WHERE status = 'active' ORDER BY name")->fetch_all(MYSQLI_ASSOC);
$cabinet_locations = $conn->query("SELECT id, name FROM cabinet_locations WHERE status = 'active' ORDER BY name")->fetch_all(MYSQLI_ASSOC);

// جلب الإحصائيات
$total_agents = $conn->query("SELECT COUNT(*) as count FROM agents")->fetch_assoc()['count'] ?? 0;
$active_agents = $conn->query("SELECT COUNT(*) as count FROM agents WHERE unms_status = 'active'")->fetch_assoc()['count'] ?? 0;
$inactive_agents = $total_agents - $active_agents;

require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <!-- رأس الصفحة المحدث -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card header-card border-0 shadow-lg animate__animated animate__fadeInDown"
                 style="background: #01233D !important; border-radius: 20px;">
                <div class="card-body p-4" style="background: transparent !important;">
                    <div class="d-flex justify-content-between align-items-center">
                        <div style="background: transparent !important;">
                            <h1 class="h2 mb-2 fw-bold" style="color: #ffffff !important; background: transparent !important; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
                                <i class="fas fa-file-export me-3" style="font-size: 2.5rem; color: #38C5E6;"></i>
                                تصدير بيانات المشتركين
                            </h1>
                            <p class="mb-0" style="font-size: 1.1rem; color: #ffffff !important; background: transparent !important; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">
                                <i class="fas fa-download me-2" style="color: #38C5E6;"></i>
                                تصدير وحفظ بيانات المشتركين بصيغة CSV مع فلاتر متقدمة
                            </p>
                        </div>
                        <div class="text-white text-center">
                            <div class="rounded-circle p-3 mb-2" style="width: 80px; height: 80px; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, #38C5E6 0%, #0B8582 100%);">
                                <i class="fas fa-chart-bar" style="font-size: 2rem; color: #ffffff;"></i>
                            </div>
                            <small style="color: #ffffff;">تقارير شاملة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="export-stats animate__animated animate__fadeInUp" style="animation-delay: 0.1s;">
                <div class="d-flex align-items-center">
                    <div class="rounded-circle bg-primary bg-opacity-10 p-3 me-3">
                        <i class="fas fa-users text-primary" style="font-size: 1.5rem;"></i>
                    </div>
                    <div>
                        <h5 class="mb-1 fw-bold"><?php echo number_format($total_agents ?? 0); ?></h5>
                        <small class="text-muted">إجمالي المشتركين</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="export-stats animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
                <div class="d-flex align-items-center">
                    <div class="rounded-circle bg-success bg-opacity-10 p-3 me-3">
                        <i class="fas fa-check-circle text-success" style="font-size: 1.5rem;"></i>
                    </div>
                    <div>
                        <h5 class="mb-1 fw-bold"><?php echo number_format($active_agents ?? 0); ?></h5>
                        <small class="text-muted">المشتركين النشطين</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="export-stats animate__animated animate__fadeInUp" style="animation-delay: 0.3s;">
                <div class="d-flex align-items-center">
                    <div class="rounded-circle bg-warning bg-opacity-10 p-3 me-3">
                        <i class="fas fa-pause-circle text-warning" style="font-size: 1.5rem;"></i>
                    </div>
                    <div>
                        <h5 class="mb-1 fw-bold"><?php echo number_format($inactive_agents ?? 0); ?></h5>
                        <small class="text-muted">غير النشطين</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="export-stats animate__animated animate__fadeInUp" style="animation-delay: 0.4s;">
                <div class="d-flex align-items-center">
                    <div class="rounded-circle bg-info bg-opacity-10 p-3 me-3">
                        <i class="fas fa-file-csv text-info" style="font-size: 1.5rem;"></i>
                    </div>
                    <div>
                        <h5 class="mb-1 fw-bold">CSV</h5>
                        <small class="text-muted">صيغة التصدير</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسائل -->
    <?php if (isset($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeInUp" role="alert" style="border-radius: 15px;">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- نموذج التصدير -->
    <div class="row">
        <div class="col-12">
            <div class="card animate__animated animate__fadeInUp" style="animation-delay: 0.5s;">
                <div class="card-header border-0 pb-0"
                     style="background: linear-gradient(135deg, #01233D 0%, #0B8582 100%); border-radius: 20px 20px 0 0;">
                    <div class="d-flex align-items-center p-3">
                        <i class="fas fa-filter text-white me-3" style="font-size: 1.5rem;"></i>
                        <h5 class="mb-0 text-white fw-bold">فلاتر التصدير</h5>
                    </div>
                </div>
                <div class="card-body p-4">

                    <form method="POST" class="row g-4">
                        <div class="col-md-6 col-lg-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-toggle-on me-2 text-primary"></i>
                                الحالة
                            </label>
                            <select name="status" class="form-select">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>

                        <div class="col-md-6 col-lg-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-cogs me-2 text-success"></i>
                                نوع الخدمة
                            </label>
                            <select name="service_type" class="form-select">
                                <option value="">جميع الأنواع</option>
                                <?php foreach ($service_types as $type): ?>
                                    <option value="<?php echo $type['id']; ?>">
                                        <?php echo htmlspecialchars($type['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-md-6 col-lg-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-building me-2 text-warning"></i>
                                اسم الفرع
                            </label>
                            <select name="branch_name" class="form-select">
                                <option value="">جميع الفروع</option>
                                <?php foreach ($branch_names as $branch): ?>
                                    <option value="<?php echo $branch['id']; ?>">
                                        <?php echo htmlspecialchars($branch['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-md-6 col-lg-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-broadcast-tower me-2 text-info"></i>
                                مكان البرج
                            </label>
                            <select name="tower_location" class="form-select">
                                <option value="">جميع الأبراج</option>
                                <?php foreach ($tower_locations as $tower): ?>
                                    <option value="<?php echo $tower['id']; ?>">
                                        <?php echo htmlspecialchars($tower['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-md-6 col-lg-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-archive me-2 text-secondary"></i>
                                مكان كابينة
                            </label>
                            <select name="cabinet_location" class="form-select">
                                <option value="">جميع الكابينات</option>
                                <?php foreach ($cabinet_locations as $cabinet): ?>
                                    <option value="<?php echo $cabinet['id']; ?>">
                                        <?php echo htmlspecialchars($cabinet['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-12">
                            <div class="d-flex gap-3 justify-content-center">
                                <button type="submit" class="btn btn-primary btn-lg px-5">
                                    <i class="fas fa-download me-2"></i>
                                    تصدير البيانات (CSV)
                                </button>
                                <button type="reset" class="btn btn-secondary btn-lg px-4">
                                    <i class="fas fa-undo me-2"></i>
                                    إعادة تعيين
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
