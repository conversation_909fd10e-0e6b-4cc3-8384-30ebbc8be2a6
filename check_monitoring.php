<?php
require_once 'config/database.php';

echo "<h2>فحص نظام المراقبة</h2>";

// فحص جدول agent_ips
echo "<h3>1. فحص جدول agent_ips:</h3>";
$result = $conn->query("SELECT COUNT(*) as total_ips, 
                               COUNT(last_ping_status) as with_ping_status, 
                               COUNT(last_ping_time) as with_ping_time,
                               COUNT(last_successful_ping) as with_successful_ping
                        FROM agent_ips");
if ($result) {
    $row = $result->fetch_assoc();
    echo "إجمالي عناوين IP: " . $row['total_ips'] . "<br>";
    echo "عناوين IP مع حالة ping: " . $row['with_ping_status'] . "<br>";
    echo "عناوين IP مع وقت ping: " . $row['with_ping_time'] . "<br>";
    echo "عناوين IP مع آخر ping ناجح: " . $row['with_successful_ping'] . "<br>";
}

// فحص جدول agent_status_logs
echo "<h3>2. فحص جدول agent_status_logs:</h3>";
$result = $conn->query("SELECT COUNT(*) as total_logs FROM agent_status_logs");
if ($result) {
    $row = $result->fetch_assoc();
    echo "إجمالي سجلات الحالة: " . $row['total_logs'] . "<br>";
}

// فحص جدول agent_uptime_stats
echo "<h3>3. فحص جدول agent_uptime_stats:</h3>";
$result = $conn->query("SELECT COUNT(*) as total_stats FROM agent_uptime_stats");
if ($result) {
    $row = $result->fetch_assoc();
    echo "إجمالي إحصائيات التوفر: " . $row['total_stats'] . "<br>";
}

// فحص جدول agents للحالة
echo "<h3>4. فحص حالة المشتركين:</h3>";
$result = $conn->query("SELECT 
                            COUNT(*) as total_agents,
                            COUNT(last_status) as with_status,
                            COUNT(last_status_change) as with_status_change,
                            SUM(CASE WHEN last_status = 'online' THEN 1 ELSE 0 END) as online_count,
                            SUM(CASE WHEN last_status = 'offline' THEN 1 ELSE 0 END) as offline_count
                        FROM agents");
if ($result) {
    $row = $result->fetch_assoc();
    echo "إجمالي المشتركين: " . $row['total_agents'] . "<br>";
    echo "مشتركين مع حالة: " . $row['with_status'] . "<br>";
    echo "مشتركين مع وقت تغيير الحالة: " . $row['with_status_change'] . "<br>";
    echo "مشتركين متصلين: " . $row['online_count'] . "<br>";
    echo "مشتركين غير متصلين: " . $row['offline_count'] . "<br>";
}

// فحص عينة من البيانات
echo "<h3>5. عينة من بيانات agent_ips:</h3>";
$result = $conn->query("SELECT ip_address, last_ping_status, last_ping_time, last_successful_ping FROM agent_ips LIMIT 5");
if ($result) {
    echo "<table border='1'>";
    echo "<tr><th>IP Address</th><th>Ping Status</th><th>Last Ping Time</th><th>Last Successful Ping</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['ip_address'] . "</td>";
        echo "<td>" . ($row['last_ping_status'] === null ? 'NULL' : $row['last_ping_status']) . "</td>";
        echo "<td>" . ($row['last_ping_time'] === null ? 'NULL' : $row['last_ping_time']) . "</td>";
        echo "<td>" . ($row['last_successful_ping'] === null ? 'NULL' : $row['last_successful_ping']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// البحث عن أي ملفات مراقبة
echo "<h3>6. البحث عن ملفات المراقبة:</h3>";
$monitoring_files = [
    'cron/ping_monitor.php',
    'cron/network_monitor.php',
    'scripts/ping_check.php',
    'monitoring/ping.php',
    'includes/ping_monitor.php',
    'includes/network_monitor.php'
];

foreach ($monitoring_files as $file) {
    if (file_exists($file)) {
        echo "✅ موجود: $file<br>";
    } else {
        echo "❌ غير موجود: $file<br>";
    }
}

// فحص إعدادات المراقبة
echo "<h3>7. فحص إعدادات المراقبة:</h3>";
$result = $conn->query("SELECT * FROM setting_categories WHERE category_key = 'monitoring'");
if ($result && $result->num_rows > 0) {
    echo "✅ فئة إعدادات المراقبة موجودة<br>";
    
    // فحص الإعدادات
    $settings_result = $conn->query("SELECT * FROM settings WHERE category_id = (SELECT id FROM setting_categories WHERE category_key = 'monitoring')");
    if ($settings_result) {
        echo "إعدادات المراقبة: " . $settings_result->num_rows . "<br>";
    }
} else {
    echo "❌ فئة إعدادات المراقبة غير موجودة<br>";
}

$conn->close();
?>
