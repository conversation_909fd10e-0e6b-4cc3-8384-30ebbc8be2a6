<?php
require_once '../includes/init.php';
$page_title = "إدارة Port";
include '../includes/header.php';

// إضافة التصميم الجديد
echo "<style>
/* تأثيرات إضافية لصفحة إدارة المنافذ */
body {
    background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
    background-size: 400% 400%;
    animation: gradientBG 15s ease infinite;
}

@keyframes gradientBG {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.container-fluid {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    margin: 1rem;
}

.card {
    transition: all 0.3s ease !important;
    border: none !important;
    border-radius: 20px !important;
    backdrop-filter: blur(10px);
    background: rgba(255,255,255,0.95) !important;
}

.card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 15px 35px rgba(0,0,0,0.15) !important;
}

.btn {
    border-radius: 15px !important;
    padding: 0.75rem 1.5rem !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    border: none !important;
}

.btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.btn-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; }
.btn-success { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%) !important; }
.btn-warning { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%) !important; }
.btn-danger { background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%) !important; }
.btn-info { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important; }

.table {
    background: rgba(255,255,255,0.95);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.table thead th {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: white;
    border: none;
    padding: 1.2rem;
    font-weight: 600;
}

.table tbody tr {
    transition: all 0.3s ease;
    border: none;
}

.table tbody tr:hover {
    background: linear-gradient(135deg, rgba(67, 233, 123, 0.1) 0%, rgba(56, 249, 215, 0.1) 100%);
    transform: translateX(5px);
}

.table tbody td {
    border: none;
    padding: 1rem;
    vertical-align: middle;
}

.form-control, .form-select, .form-control:focus, .form-select:focus {
    border-radius: 15px !important;
    border: 2px solid rgba(67, 233, 123, 0.2) !important;
    padding: 0.75rem 1rem !important;
    transition: all 0.3s ease !important;
}

.form-control:focus, .form-select:focus {
    border-color: #43e97b !important;
    box-shadow: 0 0 0 0.2rem rgba(67, 233, 123, 0.25) !important;
    transform: translateY(-2px) !important;
}

.badge {
    border-radius: 15px !important;
    padding: 0.5rem 1rem !important;
    font-weight: 600 !important;
}

.animate__fadeInUp {
    animation-duration: 0.8s;
}
</style>";

// معالجة العمليات
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        if ($_POST['action'] == 'add') {
            $name = clean($_POST['name']);
            $description = clean($_POST['description']);
            $status = clean($_POST['status']);
            
            $sql = "INSERT INTO ports (name, description, status) VALUES (?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("sss", $name, $description, $status);
            
            if ($stmt->execute()) {
                $message = "تم إضافة Port بنجاح";
                log_activity("إضافة Port", "تمت إضافة Port جديد: $name");
            } else {
                $error = "حدث خطأ أثناء إضافة Port: " . $conn->error;
            }
        }
        elseif ($_POST['action'] == 'edit') {
            $id = (int)$_POST['id'];
            $name = clean($_POST['name']);
            $description = clean($_POST['description']);
            $status = clean($_POST['status']);
            
            $sql = "UPDATE ports SET name=?, description=?, status=? WHERE id=?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("sssi", $name, $description, $status, $id);
            
            if ($stmt->execute()) {
                $message = "تم تحديث Port بنجاح";
                log_activity("تحديث Port", "تم تحديث Port: $name");
            } else {
                $error = "حدث خطأ أثناء تحديث Port: " . $conn->error;
            }
        }
        elseif ($_POST['action'] == 'delete') {
            $id = (int)$_POST['id'];
            
            // جلب اسم Port قبل الحذف
            $name_result = $conn->query("SELECT name FROM ports WHERE id = $id");
            $name = $name_result ? $name_result->fetch_assoc()['name'] : 'غير معروف';
            
            $sql = "DELETE FROM ports WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $id);
            
            if ($stmt->execute()) {
                $message = "تم حذف Port بنجاح";
                log_activity("حذف Port", "تم حذف Port: $name");
            } else {
                $error = "حدث خطأ أثناء حذف Port: " . $conn->error;
            }
        }
    }
}

// جلب جميع Ports
$sql = "SELECT * FROM ports ORDER BY created_at DESC";
$result = $conn->query($sql);
?>

<div class="container-fluid py-4">
    <!-- رأس الصفحة المحدث -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-lg animate__animated animate__fadeInDown"
                 style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); border-radius: 20px;">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="text-white">
                            <h1 class="h2 mb-2 fw-bold">
                                <i class="fas fa-ethernet me-3" style="font-size: 2.5rem;"></i>
                                إدارة المنافذ (Ports)
                            </h1>
                            <p class="mb-0 opacity-75" style="font-size: 1.1rem;">
                                <i class="fas fa-plug me-2"></i>
                                إدارة وتكوين جميع منافذ الشبكة والاتصالات
                            </p>
                        </div>
                        <div class="d-flex flex-column gap-2">
                            <button type="button" class="btn btn-light btn-lg shadow-sm"
                                    data-bs-toggle="modal" data-bs-target="#addModal"
                                    style="border-radius: 15px; font-weight: 600;">
                                <i class="fas fa-plus me-2"></i> إضافة منفذ جديد
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسائل -->
    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeInUp" role="alert" style="border-radius: 15px;">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeInUp" role="alert" style="border-radius: 15px;">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-12">
            <div class="card animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
                <div class="card-body p-4">

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الرقم</th>
                                    <th>اسم Port</th>
                                    <th>الوصف</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإضافة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($result && $result->num_rows > 0): ?>
                                    <?php while ($row = $result->fetch_assoc()): ?>
                                        <tr>
                                            <td><?php echo $row['id']; ?></td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($row['name']); ?></strong>
                                            </td>
                                            <td><?php echo htmlspecialchars($row['description'] ?? '-'); ?></td>
                                            <td>
                                                <?php if ($row['status'] == 'active'): ?>
                                                    <span class="badge bg-success">نشط</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">غير نشط</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?php echo date('Y-m-d H:i', strtotime($row['created_at'])); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                                            onclick="editPort(<?php echo htmlspecialchars(json_encode($row)); ?>)">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                            onclick="deletePort(<?php echo $row['id']; ?>, '<?php echo htmlspecialchars($row['name']); ?>')">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="6" class="text-center text-muted py-4">
                                            <i class="bi bi-inbox display-4"></i>
                                            <p class="mt-2">لا توجد Ports مضافة بعد</p>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة Port جديد -->
<div class="modal fade" id="addModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                <input type="hidden" name="action" value="add">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة Port جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">اسم Port <span class="text-danger">*</span></label>
                        <input type="text" name="name" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <textarea name="description" class="form-control" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الحالة</label>
                        <select name="status" class="form-select">
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal تعديل Port -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="id" id="edit_id">
                <div class="modal-header">
                    <h5 class="modal-title">تعديل Port</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">اسم Port <span class="text-danger">*</span></label>
                        <input type="text" name="name" id="edit_name" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <textarea name="description" id="edit_description" class="form-control" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الحالة</label>
                        <select name="status" id="edit_status" class="form-select">
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal حذف Port -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="id" id="delete_id">
                <div class="modal-header">
                    <h5 class="modal-title text-danger">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <i class="bi bi-exclamation-triangle text-warning display-4"></i>
                        <h4 class="mt-3">هل أنت متأكد؟</h4>
                        <p>سيتم حذف Port: <strong id="delete_name"></strong></p>
                        <p class="text-muted">لا يمكن التراجع عن هذا الإجراء</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">حذف</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editPort(port) {
    document.getElementById('edit_id').value = port.id;
    document.getElementById('edit_name').value = port.name;
    document.getElementById('edit_description').value = port.description || '';
    document.getElementById('edit_status').value = port.status;
    
    new bootstrap.Modal(document.getElementById('editModal')).show();
}

function deletePort(id, name) {
    document.getElementById('delete_id').value = id;
    document.getElementById('delete_name').textContent = name;
    
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>

<?php require_once '../includes/footer.php'; ?>
